# Shopper POS - Point of Sale System

A modern, comprehensive Point of Sale (POS) system built with Next.js, TypeScript, Tailwind CSS, and Prisma. Designed for supermarkets with multi-shop functionality, supporting Ghana Cedi and USD currencies.

## 🚀 Features

- **Multi-Shop Support** - Manage multiple store locations
- **Comprehensive Inventory Management** - Stock tracking, adjustments, and reporting
- **Sales Management** - Complete POS functionality with transaction tracking
- **User Management** - Role-based access (Admin, Manager, Cashier)
- **Reporting Dashboard** - Sales, inventory, and financial reports
- **Currency Support** - Ghana Cedi (GHS) and USD
- **Responsive Design** - Works on desktop, tablet, and mobile
- **Modern UI** - Built with Tailwind CSS and Flowbite components

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Flowbite React
- **Database**: SQLite with Prisma ORM
- **Authentication**: Custom JWT-based auth
- **Package Manager**: pnpm
- **Forms**: Custom forms library with validation

## 📚 Documentation

All documentation is organized in the `docs/` directory:

- **[INDEX.md](./INDEX.md)** - Complete documentation index
- **[README-Structure.md](./README-Structure.md)** - Project architecture
- **[README-Database.md](./README-Database.md)** - Database setup and usage
- **[forms-documentation.md](./forms-documentation.md)** - Forms library reference
- **[components-examples/](./components-examples/)** - Component examples
- **[snippets/](./snippets/)** - Code snippets and templates

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- pnpm (recommended package manager)

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd shopper
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Set up the database**

   ```bash
   pnpm db:push
   pnpm db:seed
   ```

5. **Start the development server**

   ```bash
   pnpm dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Default Login Credentials

After seeding the database, you can log in with:

- **Admin**: <EMAIL> / password
- **Manager**: <EMAIL> / password
- **Cashier**: <EMAIL> / password

## 📁 Project Structure

```
shopper/
├── app/                  # Next.js App Router
│   ├── (auth)/          # Authentication pages
│   ├── (pages)/         # Main application pages
│   ├── api/             # API routes
│   ├── dashboard/       # Dashboard pages
│   └── globals.css      # Global styles
├── components/          # Reusable React components
├── contexts/            # React contexts
├── docs/                # All documentation and examples
├── hooks/               # Custom React hooks
├── lib/                 # Utility libraries and configurations
├── prisma/              # Database schema and migrations
├── public/              # Static assets
├── scripts/             # Database seeding and utility scripts
└── types/               # TypeScript type definitions
```

## 🗄️ Database Scripts

```bash
# Generate Prisma client
pnpm db:generate

# Push schema changes (development)
pnpm db:push

# Create migrations (production)
pnpm db:migrate

# Seed database with sample data
pnpm db:seed

# Open Prisma Studio
pnpm db:studio

# Reset database (WARNING: deletes all data)
pnpm db:reset
```

## 🧪 Development Scripts

```bash
# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Run linting
pnpm lint

# Format code
pnpm format

# Type checking
pnpm type-check
```

## 🚀 Deployment

### Local Deployment (Raspberry Pi)

1. Build the application:

   ```bash
   pnpm build
   ```

2. Start the production server:
   ```bash
   pnpm start
   ```

### Docker Deployment

Use the provided Docker configuration for PostgreSQL:

```bash
cd postgres-podman
podman-compose up -d
```

## 📖 Learn More

- **[Next.js Documentation](https://nextjs.org/docs)** - Learn about Next.js features and API
- **[Prisma Documentation](https://www.prisma.io/docs)** - Database ORM documentation
- **[Tailwind CSS](https://tailwindcss.com/docs)** - Utility-first CSS framework
- **[Flowbite React](https://flowbite-react.com/)** - React component library

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Update documentation
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**For detailed documentation, visit the [docs/](./docs/) directory or start with [INDEX.md](./INDEX.md)**
