# Reusable Form Components

A comprehensive set of reusable, accessible, and type-safe form components built with React, TypeScript, and Tailwind CSS.

## 🚀 Features

- ✅ **Fully Accessible** - ARIA labels, keyboard navigation, screen reader support
- ✅ **Type Safe** - Complete TypeScript definitions
- ✅ **Responsive Design** - Mobile-first approach
- ✅ **Dark Mode Support** - Automatic theme switching
- ✅ **Validation Ready** - Built-in error handling and display
- ✅ **Customizable** - Flexible styling with Tailwind CSS
- ✅ **Consistent API** - Uniform props across all components

## 📦 Components

### Core Input Components

#### Input

Basic text input with variants for different types.

```tsx
import { Input, EmailInput, PasswordInput, NumberInput } from "@/components/forms";

<Input
  name="username"
  label="Username"
  placeholder="Enter username"
  required
  error={errors.username}
  helpText="Must be at least 3 characters"
/>

<EmailInput name="email" label="Email" required />
<PasswordInput name="password" label="Password" required />
<NumberInput name="quantity" label="Quantity" min={0} max={100} />
```

#### Select

Dropdown selection with predefined options.

```tsx
import { Select, CategorySelect, CurrencySelect } from "@/components/forms";

<Select
  name="status"
  label="Status"
  options={[
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" },
  ]}
  placeholder="Select status"
/>

<CategorySelect name="category" label="Product Category" />
<CurrencySelect name="currency" label="Currency" />
```

#### Textarea

Multi-line text input.

```tsx
import { Textarea } from "@/components/forms";

<Textarea
  name="description"
  label="Description"
  rows={4}
  placeholder="Enter description"
  resize="vertical"
/>;
```

#### Checkbox

Single checkbox or checkbox group.

```tsx
import { Checkbox, CheckboxGroup } from "@/components/forms";

<Checkbox
  name="terms"
  label="I agree to the terms and conditions"
  required
/>

<CheckboxGroup
  name="features"
  label="Select Features"
  options={[
    { value: "feature1", label: "Feature 1" },
    { value: "feature2", label: "Feature 2" },
  ]}
  value={selectedFeatures}
  onChange={setSelectedFeatures}
/>
```

### Specialized Components

#### CurrencyInput

Formatted currency input with locale support.

```tsx
import { CurrencyInput } from "@/components/forms";

<CurrencyInput
  name="price"
  label="Price"
  currency="GHS"
  locale="en-GH"
  value={price}
  onChange={setPrice}
  minValue={0}
/>;
```

#### SearchInput

Search input with debouncing and clear functionality.

```tsx
import { SearchInput } from "@/components/forms";

<SearchInput
  name="search"
  placeholder="Search products..."
  onSearch={handleSearch}
  debounceMs={300}
  showClearButton
/>;
```

### Layout Components

#### FormCard

Card wrapper for forms.

```tsx
import { FormCard } from "@/components/forms";

<FormCard title="User Information" description="Enter your personal details">
  {/* Form fields */}
</FormCard>;
```

#### FormGrid

Responsive grid layout.

```tsx
import { FormGrid } from "@/components/forms";

<FormGrid cols={2} gap="md">
  <Input name="firstName" label="First Name" />
  <Input name="lastName" label="Last Name" />
</FormGrid>;
```

#### FormSection

Section with title and description.

```tsx
import { FormSection } from "@/components/forms";

<FormSection title="Contact Information" description="How can we reach you?">
  {/* Form fields */}
</FormSection>;
```

#### FormActions

Button group for form actions.

```tsx
import { FormActions, SubmitButton, CancelButton } from "@/components/forms";

<FormActions align="right">
  <CancelButton>Cancel</CancelButton>
  <SubmitButton loading={isSubmitting}>Save</SubmitButton>
</FormActions>;
```

### Button Components

```tsx
import {
  Button,
  SubmitButton,
  CancelButton,
  DeleteButton,
  SaveButton,
} from "@/components/forms";

<Button variant="primary" size="md" icon={<PlusIcon />}>
  Add Item
</Button>

<SubmitButton loading={isSubmitting}>Submit</SubmitButton>
<DeleteButton onClick={handleDelete}>Delete</DeleteButton>
```

## 🎨 Styling

All components use Tailwind CSS classes and support:

- **Dark mode** - Automatic switching with `dark:` prefixes
- **Custom classes** - Add your own classes via `className` prop
- **Responsive design** - Mobile-first breakpoints
- **Focus states** - Keyboard navigation support
- **Error states** - Visual feedback for validation errors

## 🔧 Validation

Components support built-in validation display:

```tsx
<Input
  name="email"
  label="Email"
  type="email"
  required
  error={errors.email} // Shows error message
  helpText="We'll never share your email" // Shows help text
/>
```

## 📝 Complete Form Example

```tsx
import {
  FormCard,
  FormGrid,
  FormActions,
  Input,
  Select,
  CurrencyInput,
  Checkbox,
  SubmitButton,
  CancelButton,
} from "@/components/forms";

function ProductForm() {
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    price: 0,
    isActive: true,
  });

  return (
    <FormCard title="Add Product">
      <form onSubmit={handleSubmit}>
        <FormGrid cols={2} gap="md">
          <Input
            name="name"
            label="Product Name"
            required
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          />

          <Select
            name="category"
            label="Category"
            options={categoryOptions}
            value={formData.category}
            onChange={(e) =>
              setFormData({ ...formData, category: e.target.value })
            }
          />

          <CurrencyInput
            name="price"
            label="Price"
            value={formData.price}
            onChange={(value) => setFormData({ ...formData, price: value })}
          />

          <Checkbox
            name="isActive"
            label="Active Product"
            checked={formData.isActive}
            onChange={(e) =>
              setFormData({ ...formData, isActive: e.target.checked })
            }
          />
        </FormGrid>

        <FormActions>
          <CancelButton type="button">Cancel</CancelButton>
          <SubmitButton>Save Product</SubmitButton>
        </FormActions>
      </form>
    </FormCard>
  );
}
```

## 🎯 Best Practices

1. **Always provide labels** for accessibility
2. **Use appropriate input types** (email, password, number, etc.)
3. **Include error handling** for better UX
4. **Add help text** for complex fields
5. **Use FormGrid** for responsive layouts
6. **Group related fields** with FormSection
7. **Provide loading states** for async operations

## 🔗 Integration

These components integrate seamlessly with:

- **React Hook Form** - For form state management
- **Zod** - For schema validation
- **Next.js** - Server-side rendering support
- **Tailwind CSS** - For styling
- **TypeScript** - For type safety
