// Example usage of the reusable admin navigation components

import { AdminLayout, SidebarNav, TopNavbar } from "@/Navs";
import { NavLink } from "@/types";
import { useState } from "react";

// Example 1: Basic admin layout usage
export const BasicAdminExample = () => {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow dark:bg-gray-800">
            <h2 className="text-lg font-semibold mb-2">Total Users</h2>
            <p className="text-3xl font-bold text-blue-600">1,234</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow dark:bg-gray-800">
            <h2 className="text-lg font-semibold mb-2">Total Orders</h2>
            <p className="text-3xl font-bold text-green-600">567</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow dark:bg-gray-800">
            <h2 className="text-lg font-semibold mb-2">Revenue</h2>
            <p className="text-3xl font-bold text-purple-600">$12,345</p>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

// Example 2: Custom admin layout with custom links
export const CustomAdminExample = () => {
  const customLinks: NavLink[] = [
    {
      name: "Analytics",
      href: "/analytics",
      icon: <span className="h-5 w-5">📊</span>,
    },
    {
      name: "Products",
      href: "/products",
      icon: <span className="h-5 w-5">📦</span>,
    },
    {
      name: "Customers",
      href: "/customers",
      icon: <span className="h-5 w-5">👥</span>,
    },
    {
      name: "Reports",
      href: "/reports",
      icon: <span className="h-5 w-5">📈</span>,
    },
  ];

  return (
    <AdminLayout
      title="E-commerce Admin"
      sidebarLinks={customLinks}
    >
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">E-commerce Dashboard</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Welcome to your custom e-commerce admin panel.
        </p>
        {/* Your custom content here */}
      </div>
    </AdminLayout>
  );
};

// Example 3: Using components separately for more control
export const SeparateComponentsExample = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <TopNavbar
        onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
        title="Custom Layout"
      />
      <SidebarNav isCollapsed={!sidebarOpen} />

      <div className={`transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-0'} pt-16`}>
        <main className="p-4">
          <h1 className="text-2xl font-bold mb-4">Custom Implementation</h1>
          <p className="text-gray-600 dark:text-gray-400">
            This example shows how to use the navigation components separately
            for maximum flexibility and control.
          </p>
        </main>
      </div>
    </div>
  );
};
