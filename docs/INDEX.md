# Documentation Index

Welcome to the Shopper POS documentation! This directory contains all documentation, examples, and code snippets for the project.

## 📚 Main Documentation

### Core Documentation
- **[README.md](./README.md)** - Main project documentation and getting started guide
- **[README-Structure.md](./README-Structure.md)** - Project structure and architecture overview
- **[README-Database.md](./README-Database.md)** - Database setup, schema, and usage with Prisma
- **[README-Nav.md](./README-Nav.md)** - Navigation system documentation
- **[README-DTO-Validation.md](./README-DTO-Validation.md)** - Data Transfer Objects and validation

### Forms Library
- **[README-forms.md](./README-forms.md)** - Forms library overview and philosophy
- **[forms-documentation.md](./forms-documentation.md)** - Complete API reference for all form components
- **[forms-examples.tsx](./forms-examples.tsx)** - Working examples of form components
- **[pagination-documentation.md](./pagination-documentation.md)** - Pagination components documentation

### Component Documentation
- **[components-forms-README.md](./components-forms-README.md)** - Form components overview
- **[components-forms-USAGE-GUIDE.md](./components-forms-USAGE-GUIDE.md)** - Usage guide for form components

## 🔧 Examples and Code Snippets

### Component Examples
- **[components-examples/](./components-examples/)** - Component usage examples
  - `AdminLayoutExample.tsx` - Admin layout implementation examples
  - `NavExamples.tsx` - Navigation component examples

### Form Examples
- **[components-forms-examples/](./components-forms-examples/)** - Form component examples

### Code Snippets
- **[snippets/](./snippets/)** - Reusable code snippets and templates
  - `forms/` - Form-related snippets and examples
  - `sign-in/` - Sign-in page templates
  - `sign-up/` - Sign-up page templates
  - Various component snippets

## 🚀 Quick Start Guides

### For New Developers
1. Start with [README.md](./README.md) for project overview
2. Read [README-Structure.md](./README-Structure.md) to understand the architecture
3. Check [README-Database.md](./README-Database.md) for database setup
4. Explore [forms-documentation.md](./forms-documentation.md) for building forms

### For Form Development
1. Read [README-forms.md](./README-forms.md) for library philosophy
2. Check [forms-documentation.md](./forms-documentation.md) for API reference
3. Use [forms-examples.tsx](./forms-examples.tsx) for working examples
4. Reference [components-forms-USAGE-GUIDE.md](./components-forms-USAGE-GUIDE.md) for best practices

### For Component Development
1. Check [components-examples/](./components-examples/) for implementation patterns
2. Use [snippets/](./snippets/) for common code patterns
3. Reference [README-Nav.md](./README-Nav.md) for navigation components

## 📁 Directory Structure

```
docs/
├── INDEX.md                          # This file - documentation index
├── README.md                         # Main project documentation
├── README-*.md                       # Specific topic documentation
├── forms-documentation.md            # Forms API reference
├── forms-examples.tsx               # Working form examples
├── pagination-documentation.md      # Pagination components
├── components-examples/             # Component usage examples
├── components-forms-examples/       # Form component examples
└── snippets/                        # Code snippets and templates
    ├── forms/                       # Form-related snippets
    ├── sign-in/                     # Authentication templates
    ├── sign-up/                     # Registration templates
    └── *.tsx                        # Various component snippets
```

## 🔍 Finding What You Need

### I want to...
- **Set up the project** → [README.md](./README.md)
- **Understand the architecture** → [README-Structure.md](./README-Structure.md)
- **Set up the database** → [README-Database.md](./README-Database.md)
- **Build forms** → [forms-documentation.md](./forms-documentation.md)
- **See form examples** → [forms-examples.tsx](./forms-examples.tsx)
- **Use navigation components** → [README-Nav.md](./README-Nav.md)
- **Find code snippets** → [snippets/](./snippets/)
- **See component examples** → [components-examples/](./components-examples/)

## 📝 Contributing to Documentation

When adding new documentation:
1. Place it in the appropriate category
2. Update this index file
3. Use clear, descriptive filenames
4. Include examples where helpful
5. Follow the existing documentation style

## 🏷️ File Naming Convention

- `README-*.md` - Topic-specific documentation
- `*-documentation.md` - API reference documentation
- `*-examples.tsx` - Working code examples
- `components-*` - Component-specific documentation
- `snippets/` - Reusable code templates

---

**Need help?** Check the main [README.md](./README.md) or explore the relevant documentation files above.
