# DTO Types and Validation System

A comprehensive type-safe validation system using Zod schemas for forms, APIs, and database operations with enhanced form components.

## 🎯 Overview

This system provides:

- **Type-safe DTOs** for all database models
- **Enhanced form components** with password visibility toggle and improved UX
- **Form validation** with real-time error feedback
- **API validation** with structured error responses
- **Dynamic category loading** from database
- **Seamless integration** with existing form components

## 📁 File Structure

```
lib/
├── dto.ts              # DTO schemas and types
├── validation.ts       # Core validation utilities
└── form-validation.ts  # Form-specific validation hooks

components/forms/
└── ValidatedProductForm.tsx  # Example validated form

app/(pages)/examples/
├── dto-forms/page.tsx        # DTO validation examples
└── improved-forms/page.tsx   # Enhanced forms demo
```

## 🔧 Core Components

### 1. Enhanced Form Components (`lib/forms.tsx`)

#### PasswordInput with Visibility Toggle

```typescript
import { PasswordInput } from '@/lib/forms';

<PasswordInput
  name="password"
  label="Password"
  placeholder="Enter password"
  required
  value={password}
  onChange={(e) => setPassword(e.target.value)}
  error={errors.password}
  helpText="Must be at least 8 characters"
/>
```

#### Dynamic CategorySelect

```typescript
import { CategorySelect } from '@/lib/forms';

// With dynamic categories from API
const [categories, setCategories] = useState([]);

<CategorySelect
  name="categoryId"
  label="Category"
  categories={categories} // Optional: uses defaults if not provided
  value={formData.categoryId}
  onChange={(e) => updateField("categoryId", e.target.value)}
  error={errors.categoryId}
  required
/>
```

### 2. DTO Schemas (`lib/dto.ts`)

#### Base Schemas

```typescript
import { UserSchema, ProductSchema, CategorySchema } from "@/lib/dto";

// Database model types
type User = z.infer<typeof UserSchema>;
type Product = z.infer<typeof ProductSchema>;
```

#### Input DTOs

```typescript
import { CreateProductSchema, UpdateProductSchema } from "@/lib/dto";

// For API operations
type CreateProduct = z.infer<typeof CreateProductSchema>;
type UpdateProduct = z.infer<typeof UpdateProductSchema>;
```

#### Form DTOs

```typescript
import { ProductFormSchema } from "@/lib/dto";

// For form handling (handles string inputs)
type ProductForm = z.infer<typeof ProductFormSchema>;
```

### 2. Validation Utilities (`lib/validation.ts`)

#### Basic Validation

```typescript
import { validateSchema } from "@/lib/validation";

const result = validateSchema(ProductFormSchema, formData);
if (result.success) {
  console.log("Valid data:", result.data);
} else {
  console.log("Errors:", result.errors);
}
```

#### API Validation

```typescript
import { validateRequestBody } from "@/lib/validation";

// In API routes
const validatedData = await validateRequestBody(request, CreateProductSchema);
```

### 3. Form Validation Hooks (`lib/form-validation.ts`)

#### useValidatedForm Hook

```typescript
import { useValidatedForm } from "@/lib/form-validation";

const {
  formData,
  errors,
  isSubmitting,
  updateField,
  validateField,
  submitForm,
  resetForm,
} = useValidatedForm(ProductFormSchema, initialData);
```

## 🚀 Usage Examples

### 1. Simple Form Validation

```typescript
import { ProductFormSchema } from '@/lib/dto';
import { useValidatedForm, createFieldProps } from '@/lib/form-validation';

function ProductForm() {
  const { formData, errors, updateField, submitForm } = useValidatedForm(
    ProductFormSchema,
    { name: '', price: '', categoryId: '' }
  );

  const handleSubmit = async (data: ProductForm) => {
    // Data is fully validated and typed
    await saveProduct(data);
  };

  return (
    <form onSubmit={(e) => { e.preventDefault(); submitForm(handleSubmit); }}>
      <Input
        {...createFieldProps('name', formData, errors, updateField)}
        label="Product Name"
        required
      />
      {/* More fields... */}
    </form>
  );
}
```

### 2. API Route with Validation

```typescript
import { CreateProductSchema } from "@/lib/dto";
import { validateRequestBody } from "@/lib/validation";

export async function POST(request: NextRequest) {
  try {
    const validatedData = await validateRequestBody(
      request,
      CreateProductSchema,
    );

    const product = await db.product.create({
      data: validatedData,
    });

    return NextResponse.json(product);
  } catch (error: any) {
    if (error.statusCode === 400) {
      return NextResponse.json(
        { error: "Validation failed", errors: error.errors },
        { status: 400 },
      );
    }
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
```

### 3. Advanced Form with Real-time Validation

```typescript
function AdvancedProductForm() {
  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    validateField,
    submitForm,
  } = useValidatedForm(ProductFormSchema);

  return (
    <FormCard title="Create Product">
      <Input
        name="name"
        value={formData.name || ''}
        onChange={(e) => updateField('name', e.target.value)}
        onBlur={() => validateField('name', formData.name)}
        error={errors.name}
        label="Product Name"
        required
      />

      <SubmitButton
        loading={isSubmitting}
        onClick={() => submitForm(handleSubmit)}
      >
        Save Product
      </SubmitButton>
    </FormCard>
  );
}
```

## 🎨 Available Schemas

### Product Schemas

- `ProductSchema` - Full product model
- `CreateProductSchema` - For creating products
- `UpdateProductSchema` - For updating products
- `ProductFormSchema` - For form handling

### Category Schemas

- `CategorySchema` - Full category model
- `CreateCategorySchema` - For creating categories
- `CategoryFormSchema` - For form handling

### User Schemas

- `UserSchema` - Full user model
- `CreateUserSchema` - For creating users
- `UserFormSchema` - For form handling

### Sale Schemas

- `SaleSchema` - Full sale model
- `CreateSaleSchema` - For creating sales
- `SaleFormSchema` - For form handling

## 🔍 Validation Features

### Field-Level Validation

- **Required fields** - Automatic required validation
- **Type conversion** - String to number conversion for forms
- **Format validation** - Email, phone, currency formats
- **Range validation** - Min/max values for numbers
- **Custom validation** - Business logic validation

### Error Handling

- **Structured errors** - Field-specific error messages
- **Real-time feedback** - Validate on blur/change
- **Form-level errors** - General form validation errors
- **API error mapping** - Server validation errors

### Type Safety

- **Full TypeScript support** - Complete type inference
- **Runtime validation** - Zod schema validation
- **IDE support** - Autocomplete and error checking

## 🛠️ Customization

### Adding New Schemas

```typescript
// In lib/dto.ts
export const CustomFormSchema = z.object({
  field1: z.string().min(1, "Field 1 is required"),
  field2: z.number().positive("Must be positive"),
});

export type CustomForm = z.infer<typeof CustomFormSchema>;
```

### Custom Validation Rules

```typescript
// In lib/validation.ts
export const CustomValidations = {
  uniqueEmail: z
    .string()
    .email()
    .refine(async (email) => {
      const exists = await checkEmailExists(email);
      return !exists;
    }, "Email already exists"),
};
```

## 📝 Best Practices

1. **Use Form DTOs** for form handling (handles string inputs)
2. **Use Input DTOs** for API operations (proper types)
3. **Validate early** - Client and server-side validation
4. **Handle errors gracefully** - Show user-friendly messages
5. **Type everything** - Leverage TypeScript for safety

## 🔗 Integration

This system integrates seamlessly with:

- **React Hook Form** - For complex form state
- **Next.js API Routes** - Server-side validation
- **Prisma** - Database operations
- **Existing form components** - Drop-in replacement

## 🆕 Recent Improvements

### Enhanced PasswordInput Component

- ✅ **Password visibility toggle** - Eye icon to show/hide password
- ✅ **Proper state management** - Internal state for visibility toggle
- ✅ **Accessibility** - ARIA labels and keyboard navigation
- ✅ **Consistent styling** - Matches other form components
- ✅ **Error handling** - Integrated error display

### Improved CategorySelect Component

- ✅ **Dynamic categories** - Load categories from API
- ✅ **Fallback options** - Default categories if none provided
- ✅ **Type safety** - Proper TypeScript interfaces
- ✅ **Flexible usage** - Works with static or dynamic data

### Form Library Fixes

- ✅ **Fixed TypeScript errors** - Proper type definitions
- ✅ **Improved debounce function** - Better type safety
- ✅ **Enhanced validation** - Better error handling
- ✅ **Consistent API** - Uniform component interfaces

## 📚 Examples

See the complete examples at:

- `/examples/improved-forms` - Enhanced forms with password toggle and validation
- `/examples/dto-forms` - Interactive form examples
- `components/forms/ValidatedProductForm.tsx` - Production-ready form
- API routes in `app/api/` - Server-side validation examples
