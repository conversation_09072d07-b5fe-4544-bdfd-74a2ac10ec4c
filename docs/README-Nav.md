# Reusable Navigation Component

A flexible, responsive navigation component built with Next.js, TypeScript, and Tailwind CSS.

## Features

- ✅ **Fully Responsive** - Works on desktop and mobile
- ✅ **Dark Mode Support** - Automatic dark/light theme switching
- ✅ **Active Link Highlighting** - Shows current page
- ✅ **TypeScript Support** - Full type safety
- ✅ **Customizable** - Logo, links, and styling
- ✅ **Accessible** - ARIA labels and keyboard navigation
- ✅ **Mobile Menu** - Collapsible mobile navigation

## Basic Usage

```tsx
import { HeadNav } from "./components/Navs";

// Simple usage with defaults
export default function Layout() {
  return (
    <div>
      <HeadNav />
      {/* Your page content */}
    </div>
  );
}
```

## Custom Navigation Links

```tsx
import { HeadNav } from "./components/Navs";

const customLinks = [
  { name: "Dashboard", href: "/dashboard" },
  { name: "Products", href: "/products" },
  { name: "Orders", href: "/orders" },
  { name: "Settings", href: "/settings" },
];

export default function CustomNav() {
  return (
    <HeadNav
      links={customLinks}
      logo={{
        src: "/my-logo.svg",
        alt: "My Store",
        text: "My Store",
      }}
    />
  );
}
```

## Props

### HeadNavProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `links` | `NavLink[]` | Default links | Array of navigation links |
| `logo` | `LogoConfig` | Default logo | Logo configuration |
| `className` | `string` | `""` | Additional CSS classes |

### NavLink

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `name` | `string` | ✅ | Display text for the link |
| `href` | `string` | ✅ | URL path |
| `icon` | `string` | ❌ | Optional icon (future feature) |

### LogoConfig

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `src` | `string` | ✅ | Logo image path |
| `alt` | `string` | ✅ | Alt text for logo |
| `text` | `string` | ❌ | Optional text next to logo |

## Pre-built Examples

```tsx
import { EcommerceNav, MinimalNav } from "./components/Navs";

// E-commerce focused navigation
<EcommerceNav />

// Minimal navigation without logo text
<MinimalNav links={myLinks} />
```

## Styling

The component uses Tailwind CSS classes and supports:
- Custom className prop for additional styling
- Dark mode (automatic via Tailwind's dark: prefix)
- Responsive design (mobile-first approach)

## Active Link Detection

The component automatically highlights the current page based on the URL pathname. It supports:
- Exact matches (e.g., "/" matches only home)
- Prefix matches (e.g., "/products" matches "/products/123")
