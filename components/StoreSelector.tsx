"use client";

import { Select } from "@/lib/forms";
import { useEffect, useState } from "react";

interface Store {
  id: number;
  name: string;
  address: string;
  location: string;
  phoneNumber: string;
  email: string;
  logoUrl?: string | null;
}

interface StoreSelectorProps {
  selectedStoreId?: number;
  onStoreChange: (storeId: number) => void;
  className?: string;
  showAllOption?: boolean;
}

export default function StoreSelector({
  selectedStoreId,
  onStoreChange,
  className = "",
  showAllOption = false,
}: StoreSelectorProps) {
  const [stores, setStores] = useState<Store[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchStores();
  }, []);

  const fetchStores = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/stores");

      if (response.ok) {
        const data = await response.json();
        setStores(data.stores || []);

        // Auto-select first store if none selected and stores available
        if (!selectedStoreId && data.stores && data.stores.length > 0) {
          onStoreChange(data.stores[0].id);
        }
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStoreChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const storeId = parseInt(e.target.value);
    onStoreChange(storeId);
  };

  const storeOptions = [
    ...(showAllOption ? [{ value: "all", label: "All Stores" }] : []),
    ...stores.map(store => ({
      value: store.id.toString(),
      label: `${store.name} - ${store.location}`,
    })),
  ];

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-10 bg-gray-200 rounded dark:bg-gray-700"></div>
      </div>
    );
  }

  return (
    <Select
      name="storeId"
      label="Select Store"
      value={selectedStoreId?.toString() || ""}
      onChange={handleStoreChange}
      options={storeOptions}
      required
      className={className}
    />
  );
}

interface StoreCardProps {
  store: Store & {
    stats?: {
      totalUsers: number;
      totalProducts: number;
      totalSales: number;
      monthlyRevenue: number;
    };
  };
  onSelect?: (storeId: number) => void;
  isSelected?: boolean;
}

export function StoreCard({ store, onSelect, isSelected = false }: StoreCardProps) {
  return (
    <div
      className={`bg-white rounded-lg shadow-md p-6 cursor-pointer transition-all duration-200 hover:shadow-lg dark:bg-gray-800 ${
        isSelected ? "ring-2 ring-blue-500 border-blue-500" : "border border-gray-200 dark:border-gray-700"
      }`}
      onClick={() => onSelect?.(store.id)}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          {store.logoUrl ? (
            <img
              src={store.logoUrl}
              alt={`${store.name} logo`}
              className="h-12 w-12 rounded-lg object-cover"
            />
          ) : (
            <div className="h-12 w-12 rounded-lg bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <svg className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {store.name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {store.location}
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500">
              {store.address}
            </p>
          </div>
        </div>

        {isSelected && (
          <div className="flex-shrink-0">
            <div className="h-6 w-6 rounded-full bg-blue-500 flex items-center justify-center">
              <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        )}
      </div>

      <div className="mt-4 flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
        <div className="flex items-center space-x-1">
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
          <span>{store.phoneNumber}</span>
        </div>
        <div className="flex items-center space-x-1">
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          <span>{store.email}</span>
        </div>
      </div>

      {store.stats && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-500 dark:text-gray-400">Staff</p>
              <p className="font-semibold text-gray-900 dark:text-white">{store.stats.totalUsers}</p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Products</p>
              <p className="font-semibold text-gray-900 dark:text-white">{store.stats.totalProducts}</p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Sales (30d)</p>
              <p className="font-semibold text-gray-900 dark:text-white">{store.stats.totalSales}</p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Revenue (30d)</p>
              <p className="font-semibold text-green-600 dark:text-green-400">
                ₵{store.stats.monthlyRevenue.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface StoreGridProps {
  stores: (Store & {
    stats?: {
      totalUsers: number;
      totalProducts: number;
      totalSales: number;
      monthlyRevenue: number;
    };
  })[];
  selectedStoreId?: number;
  onStoreSelect?: (storeId: number) => void;
  isLoading?: boolean;
}

export function StoreGrid({ stores, selectedStoreId, onStoreSelect, isLoading = false }: StoreGridProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse dark:bg-gray-800">
            <div className="flex items-start space-x-3">
              <div className="h-12 w-12 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
              <div className="space-y-2 flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 dark:bg-gray-700"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 dark:bg-gray-700"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3 dark:bg-gray-700"></div>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="h-3 bg-gray-200 rounded dark:bg-gray-700"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4 dark:bg-gray-700"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (stores.length === 0) {
    return (
      <div className="text-center py-12">
        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No stores found</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating your first store.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {stores.map((store) => (
        <StoreCard
          key={store.id}
          store={store}
          onSelect={onStoreSelect}
          isSelected={selectedStoreId === store.id}
        />
      ))}
    </div>
  );
}
