"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: "ADMIN" | "MANAGER" | "CASHIER" | "STAFF";
  redirectTo?: string;
}

export default function ProtectedRoute({
  children,
  requiredRole,
  redirectTo = "/login",
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push(redirectTo);
        return;
      }

      // Check role-based access
      if (requiredRole && user?.role !== requiredRole) {
        // For role hierarchy, you might want to allow higher roles
        const roleHierarchy = ["STAFF", "CASHIER", "MANAGER", "ADMIN"];
        const userRoleIndex = roleHierarchy.indexOf(user?.role || "");
        const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

        if (userRoleIndex < requiredRoleIndex) {
          router.push("/unauthorized");
          return;
        }
      }
    }
  }, [isLoading, isAuthenticated, user, requiredRole, router, redirectTo]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Don't render children if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Don't render children if role check fails
  if (requiredRole && user?.role !== requiredRole) {
    const roleHierarchy = ["STAFF", "CASHIER", "MANAGER", "ADMIN"];
    const userRoleIndex = roleHierarchy.indexOf(user?.role || "");
    const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

    if (userRoleIndex < requiredRoleIndex) {
      return null;
    }
  }

  return <>{children}</>;
}
