"use client";

import AuthStoreSelector from "@/components/AuthStoreSelector";
import { useAuth } from "@/contexts/AuthContext";
import { NavLink } from "@/types";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

import {
    HiMenu,
    HiOutlineCog,
    HiOutlineShoppingCart,
    HiOutlineUsers,
    HiOutlineViewGrid,
} from "react-icons/hi";

export const defaultAdminLinks: NavLink[] = [
  {
    name: "Dashboard",
    href: "/",
    icon: <HiOutlineViewGrid className="h-5 w-5" />,
  },
  {
    name: "Users",
    href: "/users",
    icon: <HiOutlineUsers className="h-5 w-5" />,
  },
  {
    name: "Orders",
    href: "/orders",
    icon: <HiOutlineShoppingCart className="h-5 w-5" />,
  },
  {
    name: "<PERSON>ting<PERSON>",
    href: "/settings",
    icon: <HiOutlineCog className="h-5 w-5" />,
  },
];

export const SidebarNav = ({
  links = defaultAdminLinks,
  isCollapsed = false,
}: {
  links?: NavLink[];
  isCollapsed?: boolean;
}) => {
  const pathname = usePathname();

  const isActive = (href: string) =>
    pathname === href || pathname.startsWith(href);

  return (
    <aside
      className={`fixed top-12 left-0 z-40 h-screen w-64 border-r border-gray-200 bg-white transition-transform dark:border-gray-700 dark:bg-gray-800 ${
        isCollapsed ? "-translate-x-full" : "translate-x-0"
      }`}
      aria-label="Sidebar"
    >
      <div className="h-full overflow-y-auto px-3 py-4">
        <ul className="space-y-2 font-medium">
          {links.map((link) => (
            <li key={link.href}>
              <Link
                href={link.href}
                className={`group flex items-center rounded-lg p-2 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700 ${
                  isActive(link.href) ? "bg-gray-100 dark:bg-gray-700" : ""
                }`}
              >
                {link.icon}
                <span className="ml-3">{link.name}</span>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </aside>
  );
};

// User menu component
const UserMenu = () => {
  const { user, logout } = useAuth();
  const [isOpen, setIsOpen] = useState(false);

  if (!user) return null;

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none dark:text-gray-400 dark:hover:bg-gray-700"
      >
        <div className="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
          <svg
            className="h-5 w-5 text-gray-400 dark:text-gray-300"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <span className="hidden md:block">{user.name || user.email}</span>
        <svg
          className="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-gray-800">
          <div className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
            <p className="font-medium">{user.name || "User"}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">{user.email}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">{user.role}</p>
          </div>
          <div className="border-t border-gray-100 dark:border-gray-700">
            <Link
              href="/users/settings"
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
              onClick={() => setIsOpen(false)}
            >
              Settings
            </Link>
            <button
              onClick={() => {
                logout();
                setIsOpen(false);
              }}
              className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              Sign out
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export const TopNavbar = ({
  onToggleSidebar,
  title = "Admin Dashboard",
}: {
  onToggleSidebar: () => void;
  title?: string;
}) => {
  return (
    <nav className="fixed top-0 left-0 z-50 w-full border-b border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
      <div className="flex items-center justify-between px-3 py-3 lg:px-5 lg:pl-3">
        <div className="flex items-center">
          <button
            onClick={onToggleSidebar}
            className="inline-flex items-center rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none lg:hidden dark:text-gray-400 dark:hover:bg-gray-700"
            aria-label="Toggle sidebar"
          >
            <HiMenu className="h-6 w-6" />
            <span className="sr-only">Open sidebar</span>
          </button>
          <span className="ml-2 self-center text-xl font-semibold whitespace-nowrap dark:text-white lg:ml-0">
            {title}
          </span>
        </div>

        <div className="flex items-center space-x-4">
          <AuthStoreSelector />
          <UserMenu />
        </div>
      </div>
    </nav>
  );
};

// Complete admin layout component
export const AdminLayout = ({
  children,
  title = "Admin Dashboard",
  sidebarLinks = defaultAdminLinks,
}: {
  children: React.ReactNode;
  title?: string;
  sidebarLinks?: NavLink[];
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <TopNavbar onToggleSidebar={toggleSidebar} title={title} />
      <SidebarNav links={sidebarLinks} isCollapsed={sidebarCollapsed} />

      {/* Main content area */}
      <div
        className={`transition-all duration-300 ${sidebarCollapsed ? "ml-0" : "ml-64"} pt-16`}
      >
        <main className="p-4">{children}</main>
      </div>

      {/* Mobile overlay */}
      {!sidebarCollapsed && (
        <div
          className="bg-opacity-50 fixed inset-0 z-30 bg-black lg:hidden"
          onClick={toggleSidebar}
          aria-hidden="true"
        />
      )}
    </div>
  );
};
