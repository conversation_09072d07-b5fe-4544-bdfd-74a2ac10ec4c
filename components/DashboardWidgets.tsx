import { formatAmount } from "@/lib/utils";
import AnimatedCounter from "./AnimatedCounter";
import Link from "next/link";

interface StatCardProps {
  title: string;
  value: number;
  subtitle?: string;
  growth?: number;
  color: "green" | "blue" | "purple" | "yellow" | "red";
  icon: React.ReactNode;
  isLoading?: boolean;
  isCurrency?: boolean;
}

export function StatCard({
  title,
  value,
  subtitle,
  growth,
  color,
  icon,
  isLoading = false,
  isCurrency = false,
}: StatCardProps) {
  const colorClasses = {
    green: "text-green-600 dark:text-green-400",
    blue: "text-blue-600 dark:text-blue-400",
    purple: "text-purple-600 dark:text-purple-400",
    yellow: "text-yellow-600 dark:text-yellow-400",
    red: "text-red-600 dark:text-red-400",
  };

  const iconColorClasses = {
    green: "text-green-600",
    blue: "text-blue-600",
    purple: "text-purple-600",
    yellow: "text-yellow-600",
    red: "text-red-600",
  };

  if (isLoading) {
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-6 w-6 bg-gray-200 rounded dark:bg-gray-700 animate-pulse"></div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-20 dark:bg-gray-700 animate-pulse"></div>
                <div className="h-6 bg-gray-200 rounded w-16 dark:bg-gray-700 animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-24 dark:bg-gray-700 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800 hover:shadow-lg transition-shadow duration-200">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`h-6 w-6 ${iconColorClasses[color]}`}>
              {icon}
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                {title}
              </dt>
              <dd className={`text-lg font-medium ${colorClasses[color]}`}>
                {isCurrency ? (
                  <AnimatedCounter amount={value} />
                ) : (
                  value.toLocaleString()
                )}
              </dd>
              {subtitle && (
                <dd className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {subtitle}
                </dd>
              )}
              {growth !== undefined && (
                <dd className={`text-xs mt-1 ${growth >= 0 ? "text-green-600" : "text-red-600"}`}>
                  {growth >= 0 ? "+" : ""}{growth.toFixed(1)}% from previous period
                </dd>
              )}
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
}

interface ActivityItemProps {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  timestamp: string;
  amount?: number;
  status?: "success" | "warning" | "error" | "info";
}

export function ActivityItem({
  icon,
  title,
  subtitle,
  timestamp,
  amount,
  status = "success",
}: ActivityItemProps) {
  const statusClasses = {
    success: "bg-green-500",
    warning: "bg-yellow-500",
    error: "bg-red-500",
    info: "bg-blue-500",
  };

  return (
    <div className="relative flex space-x-3">
      <div>
        <span className={`h-8 w-8 rounded-full ${statusClasses[status]} flex items-center justify-center ring-8 ring-white dark:ring-gray-800`}>
          <div className="h-4 w-4 text-white">
            {icon}
          </div>
        </span>
      </div>
      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
        <div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {title}
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-500">
            {subtitle}
            {amount && (
              <span className="font-medium text-gray-900 dark:text-white ml-1">
                • {formatAmount(amount)}
              </span>
            )}
          </p>
        </div>
        <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
          {timestamp}
        </div>
      </div>
    </div>
  );
}

interface QuickActionProps {
  href: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: "blue" | "green" | "purple" | "yellow" | "red";
}

export function QuickAction({
  href,
  title,
  description,
  icon,
  color,
}: QuickActionProps) {
  const colorClasses = {
    blue: "from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700",
    green: "from-green-500 to-green-600 hover:from-green-600 hover:to-green-700",
    purple: "from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700",
    yellow: "from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700",
    red: "from-red-500 to-red-600 hover:from-red-600 hover:to-red-700",
  };

  const textColorClasses = {
    blue: "text-blue-100",
    green: "text-green-100",
    purple: "text-purple-100",
    yellow: "text-yellow-100",
    red: "text-red-100",
  };

  return (
    <Link
      href={href}
      className={`group relative bg-gradient-to-r ${colorClasses[color]} p-6 rounded-lg text-white transition-all duration-200 hover:scale-105 hover:shadow-lg`}
    >
      <div className="flex items-center justify-center mb-3">
        <div className="h-8 w-8">
          {icon}
        </div>
      </div>
      <h3 className="text-lg font-medium text-center">{title}</h3>
      <p className={`text-sm ${textColorClasses[color]} text-center mt-1`}>
        {description}
      </p>
    </Link>
  );
}

interface AlertBadgeProps {
  count: number;
  type: "low-stock" | "out-of-stock" | "pending-orders" | "overdue";
  href?: string;
}

export function AlertBadge({ count, type, href }: AlertBadgeProps) {
  const typeConfig = {
    "low-stock": {
      label: "Low Stock",
      color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100",
      icon: (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      ),
    },
    "out-of-stock": {
      label: "Out of Stock",
      color: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100",
      icon: (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
        </svg>
      ),
    },
    "pending-orders": {
      label: "Pending Orders",
      color: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100",
      icon: (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
        </svg>
      ),
    },
    "overdue": {
      label: "Overdue",
      color: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100",
      icon: (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
        </svg>
      ),
    },
  };

  const config = typeConfig[type];

  if (count === 0) {
    return null;
  }

  const content = (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.icon}
      <span className="ml-1">{count} {config.label}</span>
    </span>
  );

  if (href) {
    return (
      <Link href={href} className="hover:opacity-80 transition-opacity">
        {content}
      </Link>
    );
  }

  return content;
}

interface ProgressBarProps {
  value: number;
  max: number;
  label: string;
  color?: "blue" | "green" | "yellow" | "red";
  showPercentage?: boolean;
}

export function ProgressBar({
  value,
  max,
  label,
  color = "blue",
  showPercentage = true,
}: ProgressBarProps) {
  const percentage = max > 0 ? (value / max) * 100 : 0;
  
  const colorClasses = {
    blue: "bg-blue-600",
    green: "bg-green-600",
    yellow: "bg-yellow-600",
    red: "bg-red-600",
  };

  return (
    <div className="space-y-1">
      <div className="flex justify-between text-sm">
        <span className="text-gray-700 dark:text-gray-300">{label}</span>
        {showPercentage && (
          <span className="text-gray-500 dark:text-gray-400">
            {percentage.toFixed(1)}%
          </span>
        )}
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${colorClasses[color]}`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        ></div>
      </div>
      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
        <span>{value.toLocaleString()}</span>
        <span>{max.toLocaleString()}</span>
      </div>
    </div>
  );
}
