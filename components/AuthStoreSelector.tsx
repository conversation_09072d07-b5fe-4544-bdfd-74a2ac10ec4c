"use client";

import { useStoreContext } from "@/contexts/AuthContext";
import { Select } from "@/lib/forms";
import { useState } from "react";

export default function AuthStoreSelector() {
  const { currentStore, availableStores, switchStore, canSwitchStores } = useStoreContext();
  const [isLoading, setIsLoading] = useState(false);

  if (!canSwitchStores || availableStores.length <= 1) {
    return (
      <div className="flex items-center space-x-2">
        <svg
          className="h-5 w-5 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
          />
        </svg>
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {currentStore?.name || "No Store Selected"}
        </span>
      </div>
    );
  }

  const storeOptions = availableStores.map(store => ({
    value: store.id.toString(),
    label: `${store.name} - ${store.location}`,
  }));

  const handleStoreChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const storeId = parseInt(e.target.value);
    if (storeId === currentStore?.id) return;

    setIsLoading(true);
    try {
      await switchStore(storeId);
      // Optionally show success message
    } catch (error) {
      console.error("Failed to switch store:", error);
      // Optionally show error message
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <svg
        className="h-5 w-5 text-gray-400"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
        />
      </svg>
      <div className="min-w-[200px]">
        <Select
          name="store"
          value={currentStore?.id.toString() || ""}
          onChange={handleStoreChange}
          options={storeOptions}
          disabled={isLoading}
          className="text-sm"
        />
      </div>
      {isLoading && (
        <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-blue-600"></div>
      )}
    </div>
  );
}
