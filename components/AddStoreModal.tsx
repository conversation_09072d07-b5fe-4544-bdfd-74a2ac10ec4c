"use client";

import { useState } from "react";
import {
  FormCard,
  FormGrid,
  FormActions,
  Input,
  Textarea,
  Button,
} from "@/lib/forms";

interface AddStoreModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStoreAdded: () => void;
}

export default function AddStoreModal({ isOpen, onClose, onStoreAdded }: AddStoreModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    address: "",
    location: "",
    phoneNumber: "",
    email: "",
    website: "",
    logoUrl: "",
    ceo: "",
    ceoContact: "",
    ceoEmail: "",
    facebook: "",
    whatsapp: "",
    telegram: "",
    visionStatement: "",
    missionStatement: "",
    mainGoal: "",
    mainObjective: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = "Store name is required";
    if (!formData.address.trim()) newErrors.address = "Address is required";
    if (!formData.location.trim()) newErrors.location = "Location is required";
    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = "Phone number is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!formData.ceo.trim()) newErrors.ceo = "CEO name is required";
    if (!formData.ceoContact.trim()) newErrors.ceoContact = "CEO contact is required";
    if (!formData.ceoEmail.trim()) newErrors.ceoEmail = "CEO email is required";

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
    if (formData.ceoEmail && !emailRegex.test(formData.ceoEmail)) {
      newErrors.ceoEmail = "Please enter a valid CEO email address";
    }

    // Phone validation (basic)
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
    if (formData.phoneNumber && !phoneRegex.test(formData.phoneNumber)) {
      newErrors.phoneNumber = "Please enter a valid phone number";
    }
    if (formData.ceoContact && !phoneRegex.test(formData.ceoContact)) {
      newErrors.ceoContact = "Please enter a valid CEO contact number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/stores", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Store created:", data);
        
        // Reset form
        setFormData({
          name: "",
          address: "",
          location: "",
          phoneNumber: "",
          email: "",
          website: "",
          logoUrl: "",
          ceo: "",
          ceoContact: "",
          ceoEmail: "",
          facebook: "",
          whatsapp: "",
          telegram: "",
          visionStatement: "",
          missionStatement: "",
          mainGoal: "",
          mainObjective: "",
        });
        
        onStoreAdded();
        onClose();
      } else {
        const errorData = await response.json();
        setErrors({ general: errorData.error || "Failed to create store" });
      }
    } catch (error) {
      console.error("Error creating store:", error);
      setErrors({ general: "Failed to create store. Please try again." });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full dark:bg-gray-800">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 dark:bg-gray-800">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                  Add New Store
                </h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {errors.general && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md dark:bg-red-900/20 dark:border-red-800">
                  <p className="text-sm text-red-600 dark:text-red-400">{errors.general}</p>
                </div>
              )}

              <div className="space-y-6">
                {/* Basic Information */}
                <FormCard title="Basic Information">
                  <FormGrid cols={2} gap="md">
                    <Input
                      name="name"
                      label="Store Name"
                      value={formData.name}
                      onChange={handleInputChange}
                      error={errors.name}
                      required
                    />
                    <Input
                      name="location"
                      label="Location/City"
                      value={formData.location}
                      onChange={handleInputChange}
                      error={errors.location}
                      required
                    />
                  </FormGrid>
                  <Textarea
                    name="address"
                    label="Full Address"
                    value={formData.address}
                    onChange={handleInputChange}
                    error={errors.address}
                    required
                    rows={2}
                  />
                  <FormGrid cols={2} gap="md">
                    <Input
                      name="phoneNumber"
                      label="Phone Number"
                      type="tel"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                      error={errors.phoneNumber}
                      required
                    />
                    <Input
                      name="email"
                      label="Email Address"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      error={errors.email}
                      required
                    />
                  </FormGrid>
                  <FormGrid cols={2} gap="md">
                    <Input
                      name="website"
                      label="Website (Optional)"
                      type="url"
                      value={formData.website}
                      onChange={handleInputChange}
                      error={errors.website}
                    />
                    <Input
                      name="logoUrl"
                      label="Logo URL (Optional)"
                      type="url"
                      value={formData.logoUrl}
                      onChange={handleInputChange}
                      error={errors.logoUrl}
                    />
                  </FormGrid>
                </FormCard>

                {/* CEO Information */}
                <FormCard title="CEO/Manager Information">
                  <FormGrid cols={3} gap="md">
                    <Input
                      name="ceo"
                      label="CEO/Manager Name"
                      value={formData.ceo}
                      onChange={handleInputChange}
                      error={errors.ceo}
                      required
                    />
                    <Input
                      name="ceoContact"
                      label="CEO Contact"
                      type="tel"
                      value={formData.ceoContact}
                      onChange={handleInputChange}
                      error={errors.ceoContact}
                      required
                    />
                    <Input
                      name="ceoEmail"
                      label="CEO Email"
                      type="email"
                      value={formData.ceoEmail}
                      onChange={handleInputChange}
                      error={errors.ceoEmail}
                      required
                    />
                  </FormGrid>
                </FormCard>

                {/* Social Media (Optional) */}
                <FormCard title="Social Media (Optional)">
                  <FormGrid cols={3} gap="md">
                    <Input
                      name="facebook"
                      label="Facebook"
                      value={formData.facebook}
                      onChange={handleInputChange}
                      error={errors.facebook}
                    />
                    <Input
                      name="whatsapp"
                      label="WhatsApp"
                      value={formData.whatsapp}
                      onChange={handleInputChange}
                      error={errors.whatsapp}
                    />
                    <Input
                      name="telegram"
                      label="Telegram"
                      value={formData.telegram}
                      onChange={handleInputChange}
                      error={errors.telegram}
                    />
                  </FormGrid>
                </FormCard>

                {/* Business Information (Optional) */}
                <FormCard title="Business Information (Optional)">
                  <FormGrid cols={2} gap="md">
                    <Textarea
                      name="visionStatement"
                      label="Vision Statement"
                      value={formData.visionStatement}
                      onChange={handleInputChange}
                      error={errors.visionStatement}
                      rows={3}
                    />
                    <Textarea
                      name="missionStatement"
                      label="Mission Statement"
                      value={formData.missionStatement}
                      onChange={handleInputChange}
                      error={errors.missionStatement}
                      rows={3}
                    />
                  </FormGrid>
                  <FormGrid cols={2} gap="md">
                    <Textarea
                      name="mainGoal"
                      label="Main Goal"
                      value={formData.mainGoal}
                      onChange={handleInputChange}
                      error={errors.mainGoal}
                      rows={2}
                    />
                    <Textarea
                      name="mainObjective"
                      label="Main Objective"
                      value={formData.mainObjective}
                      onChange={handleInputChange}
                      error={errors.mainObjective}
                      rows={2}
                    />
                  </FormGrid>
                </FormCard>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse dark:bg-gray-700">
              <FormActions align="right">
                <Button
                  type="submit"
                  variant="primary"
                  loading={isSubmitting}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Creating..." : "Create Store"}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              </FormActions>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
