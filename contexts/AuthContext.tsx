"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

interface User {
  id: number;
  email: string;
  name: string | null;
  role: "ADMIN" | "MANAGER" | "CASHIER" | "STAFF";
  storeId: number;
  store?: {
    id: number;
    name: string;
    location: string;
  };
}

interface Store {
  id: number;
  name: string;
  address: string;
  location: string;
  phoneNumber: string;
  email: string;
  logoUrl?: string | null;
}

interface AuthContextType {
  user: User | null;
  currentStore: Store | null;
  availableStores: Store[];
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  switchStore: (storeId: number) => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [currentStore, setCurrentStore] = useState<Store | null>(null);
  const [availableStores, setAvailableStores] = useState<Store[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Load user session on mount
  useEffect(() => {
    loadUserSession();
  }, []);

  // Load user session from localStorage/sessionStorage
  const loadUserSession = async () => {
    try {
      setIsLoading(true);
      
      // Check for stored session
      const storedUser = localStorage.getItem("user");
      const storedStoreId = localStorage.getItem("currentStoreId");
      
      if (storedUser) {
        const userData = JSON.parse(storedUser);
        setUser(userData);
        
        // Load available stores
        await loadAvailableStores(userData);
        
        // Set current store (either stored preference or user's assigned store)
        const storeId = storedStoreId ? parseInt(storedStoreId) : userData.storeId;
        await setCurrentStoreById(storeId);
      }
    } catch (error) {
      console.error("Error loading user session:", error);
      // Clear invalid session data
      localStorage.removeItem("user");
      localStorage.removeItem("currentStoreId");
    } finally {
      setIsLoading(false);
    }
  };

  // Load available stores based on user role
  const loadAvailableStores = async (userData: User) => {
    try {
      const response = await fetch("/api/stores");
      if (response.ok) {
        const data = await response.json();
        const stores = data.stores || [];
        
        // ADMIN users can access all stores, others only their assigned store
        if (userData.role === "ADMIN") {
          setAvailableStores(stores);
        } else {
          // Filter to only the user's assigned store
          const userStore = stores.find((store: Store) => store.id === userData.storeId);
          setAvailableStores(userStore ? [userStore] : []);
        }
      }
    } catch (error) {
      console.error("Error loading stores:", error);
    }
  };

  // Set current store by ID
  const setCurrentStoreById = async (storeId: number) => {
    const store = availableStores.find(s => s.id === storeId);
    if (store) {
      setCurrentStore(store);
      localStorage.setItem("currentStoreId", storeId.toString());
    } else if (availableStores.length > 0) {
      // Fallback to first available store
      setCurrentStore(availableStores[0]);
      localStorage.setItem("currentStoreId", availableStores[0].id.toString());
    }
  };

  // Login function
  const login = async (email: string, password: string) => {
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Login failed");
      }

      const { user: userData, token } = await response.json();
      
      // Store user data and token
      localStorage.setItem("user", JSON.stringify(userData));
      localStorage.setItem("authToken", token);
      
      setUser(userData);
      
      // Load available stores and set current store
      await loadAvailableStores(userData);
      
      // Auto-select user's assigned store
      await setCurrentStoreById(userData.storeId);
      
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  };

  // Logout function
  const logout = () => {
    setUser(null);
    setCurrentStore(null);
    setAvailableStores([]);
    localStorage.removeItem("user");
    localStorage.removeItem("authToken");
    localStorage.removeItem("currentStoreId");
    
    // Redirect to login page
    window.location.href = "/login";
  };

  // Switch store (only for ADMIN users or if user has access)
  const switchStore = async (storeId: number) => {
    if (!user) return;
    
    // Check if user has access to this store
    const hasAccess = user.role === "ADMIN" || user.storeId === storeId;
    if (!hasAccess) {
      throw new Error("You don't have access to this store");
    }
    
    await setCurrentStoreById(storeId);
  };

  // Refresh user data
  const refreshUser = async () => {
    if (!user) return;
    
    try {
      const response = await fetch(`/api/users/${user.id}`);
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        localStorage.setItem("user", JSON.stringify(userData));
        
        // Reload stores in case user's access changed
        await loadAvailableStores(userData);
      }
    } catch (error) {
      console.error("Error refreshing user:", error);
    }
  };

  const value: AuthContextType = {
    user,
    currentStore,
    availableStores,
    isLoading,
    isAuthenticated,
    login,
    logout,
    switchStore,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

// Hook for getting current store context
export function useStoreContext() {
  const { currentStore, availableStores, switchStore, user } = useAuth();
  
  return {
    currentStore,
    availableStores,
    switchStore,
    canSwitchStores: user?.role === "ADMIN" && availableStores.length > 1,
  };
}
