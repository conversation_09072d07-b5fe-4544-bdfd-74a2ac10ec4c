#!/usr/bin/env bash
# dev.sh – start Postgres (if needed) and then launch Next.js and Prisma Studio

set -euo pipefail

# ──────────────── Configuration ────────────────
COMPOSE_FILE="postgres-podman/podman-compose.yml" # path to podman-compose.yml
SERVICE_NAME="postgres_db"                        # container service name
DB_USER="shopper_user"                            # container user
NEXT_DEV_CMD="pnpm dev"
STUDIO_CMD="npx prisma studio"                    # command to launch Prisma Studio
# ───────────────────────────────────────────────

is_container_created() {
  podman container exists "$SERVICE_NAME"
}

is_running() {
  podman ps --format '{{.Names}}' | grep -q "^${SERVICE_NAME}$"
}

start_db() {
  echo "🔄  Starting PostgreSQL container…"
  if is_container_created; then
    if is_running; then
      echo "✅  $SERVICE_NAME is already running."
    else
      echo "♻️  Starting existing container $SERVICE_NAME"
      podman start "$SERVICE_NAME"
    fi
  else
    echo "📦  No container found. Running podman-compose…"
    podman-compose -f "$COMPOSE_FILE" up -d
  fi
}

wait_for_db() {
  echo "⏳  Waiting for PostgreSQL to be ready…"
  until podman exec "$SERVICE_NAME" pg_isready -U "$DB_USER" > /dev/null 2>&1; do
    sleep 1
  done
  echo "✅  PostgreSQL is ready!"
}

open_prisma_studio() {
  echo "🧭  Opening Prisma Studio in the background…"
  $STUDIO_CMD &
  STUDIO_PID=$!

  # Try opening browser automatically (Linux/macOS only)
  sleep 2
  # xdg-open http://localhost:5555 >/dev/null 2>&1 || open http://localhost:5555 || true
}

cleanup() {
  if [[ -n "${STUDIO_PID:-}" ]]; then
    echo "🛑  Shutting down Prisma Studio (PID $STUDIO_PID)…"
    kill "$STUDIO_PID" 2>/dev/null || true
  fi
}
trap cleanup EXIT

start_next_dev() {
  echo "🚀  Starting Next.js development server…"
  exec $NEXT_DEV_CMD
}

# ───── Execution ─────
start_db
wait_for_db
open_prisma_studio
start_next_dev
