import { db, prisma } from "@/lib/db";
import { UpdateUserSchema } from "@/lib/dto";
import { validateRequestBody } from "@/lib/validation";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const user = await db.users.getById(parseInt(id));

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 },
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    // Check if user exists
    const existingUser = await db.users.getById(parseInt(id));
    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Validate request body using DTO schema
    const validatedData = await validateRequestBody(request, UpdateUserSchema);

    const user = await prisma.user.update({
      where: { id: parseInt(id) },
      data: validatedData,
    });

    return NextResponse.json(user);
  } catch (error: any) {
    console.error("Error updating user:", error);

    // Handle validation errors
    if (error.statusCode === 400) {
      return NextResponse.json(
        {
          error: "Validation failed",
          errors: error.errors,
          fieldErrors: error.fieldErrors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 },
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    // Check if user exists
    const existingUser = await db.users.getById(parseInt(id));
    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Delete the user
    await prisma.user.delete({
      where: { id: parseInt(id) },
    });

    return NextResponse.json({ message: "User deleted successfully" });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 },
    );
  }
}
