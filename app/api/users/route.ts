import { prisma } from "@/lib/db";
import { CreateUserSchema } from "@/lib/dto";
import { validateRequestBody } from "@/lib/validation";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const page = parseInt(searchParams.get("page") || "1");
    const search = searchParams.get("search") || "";
    const role = searchParams.get("role") || "";
    const storeId = searchParams.get("storeId");

    // Build where clause
    const whereClause: any = {};

    if (storeId) {
      whereClause.storeId = parseInt(storeId);
    }

    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { staffId: { contains: search, mode: "insensitive" } },
      ];
    }

    if (role && role !== "all") {
      whereClause.role = role.toUpperCase();
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Fetch users with pagination
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where: whereClause,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          staffId: true,
          gender: true,
          phone: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          store: {
            select: {
              id: true,
              name: true,
              location: true,
            },
          },
        },
        orderBy: { name: "asc" },
        skip: offset,
        take: limit,
      }),

      prisma.user.count({
        where: whereClause,
      }),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      users,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
        hasNextPage,
        hasPreviousPage,
      },
      filters: {
        search,
        role,
        storeId,
      },
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Validate request body using DTO schema
    const validatedData = await validateRequestBody(request, CreateUserSchema);

    // Hash the password
    const bcrypt = require("bcryptjs");
    const hashedPassword = await bcrypt.hash(validatedData.password, 12);

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "A user with this email already exists" },
        { status: 409 },
      );
    }

    // Create user with hashed password
    const { password, ...userData } = validatedData;
    const user = await prisma.user.create({
      data: {
        ...userData,
        password: hashedPassword,
      },
      include: {
        store: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
    });

    // Remove password from response
    const { password: _, ...userResponse } = user;
    return NextResponse.json(userResponse, { status: 201 });
  } catch (error: any) {
    console.error("Error creating user:", error);

    // Handle validation errors
    if (error.statusCode === 400) {
      return NextResponse.json(
        {
          error: "Validation failed",
          errors: error.errors,
          fieldErrors: error.fieldErrors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 },
    );
  }
}
