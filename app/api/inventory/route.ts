import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const page = parseInt(searchParams.get("page") || "1");
    const search = searchParams.get("search") || "";
    const category = searchParams.get("category") || "";
    const status = searchParams.get("status") || ""; // all, good, low, out
    const storeId = searchParams.get("storeId");

    // Build where clause
    const whereClause: any = {
      isActive: true,
    };

    if (storeId) {
      whereClause.storeId = parseInt(storeId);
    }

    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { sku: { contains: search, mode: "insensitive" } },
        { barcode: { contains: search, mode: "insensitive" } },
      ];
    }

    if (category && category !== "all") {
      whereClause.category = {
        name: { contains: category, mode: "insensitive" },
      };
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Fetch products with inventory information
    const [products, totalCount] = await Promise.all([
      prisma.product.findMany({
        where: whereClause,
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          store: {
            select: {
              id: true,
              name: true,
            },
          },
          stockAdjustments: {
            orderBy: { createdAt: "desc" },
            take: 1,
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        orderBy: { name: "asc" },
        skip: offset,
        take: limit,
      }),
      
      prisma.product.count({
        where: whereClause,
      }),
    ]);

    // Transform products to inventory items with status
    const inventoryItems = products.map((product) => {
      const stockLevel = product.stock;
      const minStock = product.minStock || 10;
      
      let stockStatus = "good";
      if (stockLevel === 0) {
        stockStatus = "out";
      } else if (stockLevel <= minStock) {
        stockStatus = "low";
      }

      const lastAdjustment = product.stockAdjustments[0];

      return {
        id: product.id,
        name: product.name,
        sku: product.sku,
        barcode: product.barcode,
        category: product.category.name,
        currentStock: stockLevel,
        minStock: minStock,
        maxStock: 1000, // You might want to add this field to the product model
        value: stockLevel * product.price,
        status: stockStatus,
        location: "Main Warehouse", // You might want to add this field to the product model
        lastAdjustment: lastAdjustment ? {
          type: lastAdjustment.type,
          quantity: lastAdjustment.quantity,
          reason: lastAdjustment.reason,
          adjustedBy: lastAdjustment.user.name,
          date: lastAdjustment.createdAt,
        } : null,
        price: product.price,
        cost: product.cost,
        store: product.store,
      };
    });

    // Filter by status if specified
    let filteredItems = inventoryItems;
    if (status && status !== "all") {
      filteredItems = inventoryItems.filter(item => item.status === status);
    }

    // Calculate summary statistics
    const totalValue = filteredItems.reduce((sum, item) => sum + item.value, 0);
    const lowStockCount = inventoryItems.filter(item => item.status === "low").length;
    const outOfStockCount = inventoryItems.filter(item => item.status === "out").length;
    const goodStockCount = inventoryItems.filter(item => item.status === "good").length;

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      inventory: filteredItems,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
        hasNextPage,
        hasPreviousPage,
      },
      summary: {
        totalItems: inventoryItems.length,
        totalValue,
        lowStockCount,
        outOfStockCount,
        goodStockCount,
      },
      filters: {
        search,
        category,
        status,
        storeId,
      },
    });
  } catch (error) {
    console.error("Error fetching inventory:", error);
    return NextResponse.json(
      { error: "Failed to fetch inventory data" },
      { status: 500 }
    );
  }
}
