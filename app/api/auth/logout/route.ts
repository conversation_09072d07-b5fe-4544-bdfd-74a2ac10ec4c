import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    // In a more complex setup, you might want to:
    // 1. Invalidate the JWT token on the server side
    // 2. Clear any server-side sessions
    // 3. Log the logout event

    return NextResponse.json({
      message: "Logout successful",
    });

  } catch (error) {
    console.error("Logout error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
