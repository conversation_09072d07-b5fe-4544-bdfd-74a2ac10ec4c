import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20");
    const days = parseInt(searchParams.get("days") || "30");

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    startDate.setHours(0, 0, 0, 0);

    // Get customers with their sales data
    const customers = await prisma.customer.findMany({
      include: {
        sales: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
            status: "COMPLETED",
          },
          include: {
            saleItems: {
              include: {
                product: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
      },
    });

    // Calculate customer statistics
    const customerStats = customers
      .map(customer => {
        const sales = customer.sales;
        const totalSpent = sales.reduce((sum, sale) => sum + sale.total, 0);
        const totalVisits = sales.length;
        const averageOrderValue = totalVisits > 0 ? totalSpent / totalVisits : 0;
        
        // Get last visit date
        const lastVisit = sales.length > 0 
          ? sales[0].createdAt // Already sorted by createdAt desc
          : null;

        // Calculate total items purchased
        const totalItems = sales.reduce((sum, sale) => {
          return sum + sale.saleItems.reduce((itemSum, item) => itemSum + item.quantity, 0);
        }, 0);

        // Get favorite categories
        const categoryPurchases = sales.reduce((acc: any, sale) => {
          sale.saleItems.forEach(item => {
            const categoryName = item.product.category || "Uncategorized";
            if (!acc[categoryName]) {
              acc[categoryName] = { count: 0, total: 0 };
            }
            acc[categoryName].count += item.quantity;
            acc[categoryName].total += item.total;
          });
          return acc;
        }, {});

        const favoriteCategory = Object.entries(categoryPurchases)
          .sort(([,a]: any, [,b]: any) => b.total - a.total)[0]?.[0] || null;

        // Calculate customer lifetime value (simple version)
        const firstPurchase = sales.length > 0 
          ? sales[sales.length - 1].createdAt 
          : null;
        
        let customerLifetimeDays = 0;
        if (firstPurchase) {
          customerLifetimeDays = Math.max(1, Math.floor((endDate.getTime() - firstPurchase.getTime()) / (1000 * 60 * 60 * 24)));
        }

        const averageSpendingPerDay = customerLifetimeDays > 0 ? totalSpent / customerLifetimeDays : 0;

        return {
          id: customer.id,
          name: customer.name,
          email: customer.email,
          phone: customer.phone,
          totalSpent,
          totalVisits,
          averageOrderValue,
          lastVisit,
          totalItems,
          favoriteCategory,
          customerLifetimeDays,
          averageSpendingPerDay,
          // Only include customers who made purchases in the period
          hasPurchases: totalSpent > 0,
        };
      })
      .filter(customer => customer.hasPurchases)
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, limit);

    // Calculate summary statistics
    const summary = {
      totalCustomers: customerStats.length,
      totalRevenue: customerStats.reduce((sum, customer) => sum + customer.totalSpent, 0),
      averageCustomerValue: customerStats.length > 0 
        ? customerStats.reduce((sum, customer) => sum + customer.totalSpent, 0) / customerStats.length 
        : 0,
      averageOrderValue: customerStats.length > 0
        ? customerStats.reduce((sum, customer) => sum + customer.averageOrderValue, 0) / customerStats.length
        : 0,
      totalVisits: customerStats.reduce((sum, customer) => sum + customer.totalVisits, 0),
      totalItems: customerStats.reduce((sum, customer) => sum + customer.totalItems, 0),
    };

    // Get customer segments
    const segments = {
      highValue: customerStats.filter(c => c.totalSpent >= summary.averageCustomerValue * 2).length,
      mediumValue: customerStats.filter(c => 
        c.totalSpent >= summary.averageCustomerValue && 
        c.totalSpent < summary.averageCustomerValue * 2
      ).length,
      lowValue: customerStats.filter(c => c.totalSpent < summary.averageCustomerValue).length,
    };

    // Get recent new customers (registered in the period)
    const newCustomers = await prisma.customer.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: { createdAt: "desc" },
      take: 10,
    });

    return NextResponse.json({
      customers: customerStats,
      summary,
      segments,
      newCustomers,
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        days,
      },
    });
  } catch (error) {
    console.error("Error fetching top customers:", error);
    return NextResponse.json(
      { error: "Failed to fetch top customers" },
      { status: 500 }
    );
  }
}
