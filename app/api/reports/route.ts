import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get("type") || "sales";
    const dateRange = searchParams.get("dateRange") || "last30days";
    const category = searchParams.get("category") || "all";
    const paymentMethod = searchParams.get("paymentMethod") || "all";

    // Calculate date range
    const { startDate, endDate } = getDateRange(dateRange);

    switch (reportType) {
      case "sales":
        return NextResponse.json(await getSalesReport(startDate, endDate, paymentMethod));
      
      case "inventory":
        return NextResponse.json(await getInventoryReport(category));
      
      case "products":
        return NextResponse.json(await getProductsReport(startDate, endDate, category));
      
      case "customers":
        return NextResponse.json(await getCustomersReport(startDate, endDate));
      
      case "financial":
        return NextResponse.json(await getFinancialReport(startDate, endDate));
      
      case "tax":
        return NextResponse.json(await getTaxReport(startDate, endDate));
      
      default:
        return NextResponse.json(await getSalesReport(startDate, endDate, paymentMethod));
    }
  } catch (error) {
    console.error("Error generating report:", error);
    return NextResponse.json(
      { error: "Failed to generate report" },
      { status: 500 }
    );
  }
}

function getDateRange(dateRange: string) {
  const now = new Date();
  let startDate: Date;
  let endDate: Date = new Date(now);

  switch (dateRange) {
    case "today":
      startDate = new Date(now);
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    
    case "yesterday":
      startDate = new Date(now);
      startDate.setDate(startDate.getDate() - 1);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(startDate);
      endDate.setHours(23, 59, 59, 999);
      break;
    
    case "last7days":
      startDate = new Date(now);
      startDate.setDate(startDate.getDate() - 7);
      startDate.setHours(0, 0, 0, 0);
      break;
    
    case "last30days":
      startDate = new Date(now);
      startDate.setDate(startDate.getDate() - 30);
      startDate.setHours(0, 0, 0, 0);
      break;
    
    case "thismonth":
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    
    case "lastmonth":
      startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      endDate = new Date(now.getFullYear(), now.getMonth(), 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    
    case "thisquarter":
      const quarter = Math.floor(now.getMonth() / 3);
      startDate = new Date(now.getFullYear(), quarter * 3, 1);
      break;
    
    case "thisyear":
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    
    default:
      startDate = new Date(now);
      startDate.setDate(startDate.getDate() - 30);
      startDate.setHours(0, 0, 0, 0);
  }

  return { startDate, endDate };
}

async function getSalesReport(startDate: Date, endDate: Date, paymentMethod: string) {
  const whereClause: any = {
    createdAt: {
      gte: startDate,
      lte: endDate,
    },
    status: "COMPLETED",
  };

  if (paymentMethod !== "all") {
    whereClause.paymentType = paymentMethod.toUpperCase();
  }

  const sales = await prisma.sale.findMany({
    where: whereClause,
    include: {
      saleItems: {
        include: {
          product: {
            include: {
              category: true,
            },
          },
        },
      },
      user: true,
      customer: true,
    },
    orderBy: { createdAt: "desc" },
  });

  // Calculate summary statistics
  const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
  const totalTax = sales.reduce((sum, sale) => sum + (sale.tax || 0), 0);
  const totalDiscount = sales.reduce((sum, sale) => sum + (sale.discount || 0), 0);
  const averageOrderValue = sales.length > 0 ? totalSales / sales.length : 0;

  // Group by payment method
  const paymentMethodBreakdown = sales.reduce((acc: any, sale) => {
    const method = sale.paymentType;
    if (!acc[method]) {
      acc[method] = { count: 0, total: 0 };
    }
    acc[method].count++;
    acc[method].total += sale.total;
    return acc;
  }, {});

  // Daily sales trend
  const dailySales = sales.reduce((acc: any, sale) => {
    const date = sale.createdAt.toISOString().split('T')[0];
    if (!acc[date]) {
      acc[date] = { date, total: 0, count: 0 };
    }
    acc[date].total += sale.total;
    acc[date].count++;
    return acc;
  }, {});

  return {
    summary: {
      totalSales,
      totalTax,
      totalDiscount,
      averageOrderValue,
      totalTransactions: sales.length,
    },
    paymentMethodBreakdown,
    dailySales: Object.values(dailySales),
    recentSales: sales.slice(0, 10),
  };
}

async function getInventoryReport(category: string) {
  const whereClause: any = {
    isActive: true,
  };

  if (category !== "all") {
    whereClause.category = {
      name: {
        contains: category,
        mode: "insensitive",
      },
    };
  }

  const products = await prisma.product.findMany({
    where: whereClause,
    include: {
      category: true,
      stockAdjustments: {
        orderBy: { createdAt: "desc" },
        take: 5,
        include: {
          user: true,
        },
      },
    },
    orderBy: { name: "asc" },
  });

  // Calculate inventory statistics
  const totalProducts = products.length;
  const totalStockValue = products.reduce((sum, product) => sum + (product.stock * product.price), 0);
  const lowStockProducts = products.filter(product => product.stock <= product.minStock);
  const outOfStockProducts = products.filter(product => product.stock === 0);

  // Group by category
  const categoryBreakdown = products.reduce((acc: any, product) => {
    const categoryName = product.category.name;
    if (!acc[categoryName]) {
      acc[categoryName] = {
        name: categoryName,
        productCount: 0,
        totalStock: 0,
        totalValue: 0,
        lowStockCount: 0,
      };
    }
    acc[categoryName].productCount++;
    acc[categoryName].totalStock += product.stock;
    acc[categoryName].totalValue += product.stock * product.price;
    if (product.stock <= product.minStock) {
      acc[categoryName].lowStockCount++;
    }
    return acc;
  }, {});

  return {
    summary: {
      totalProducts,
      totalStockValue,
      lowStockCount: lowStockProducts.length,
      outOfStockCount: outOfStockProducts.length,
    },
    categoryBreakdown: Object.values(categoryBreakdown),
    lowStockProducts: lowStockProducts.slice(0, 20),
    outOfStockProducts: outOfStockProducts.slice(0, 20),
    recentAdjustments: products
      .flatMap(product => 
        product.stockAdjustments.map(adj => ({
          ...adj,
          product: { name: product.name, sku: product.sku },
        }))
      )
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10),
  };
}

async function getProductsReport(startDate: Date, endDate: Date, category: string) {
  const whereClause: any = {
    sale: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: "COMPLETED",
    },
  };

  if (category !== "all") {
    whereClause.product = {
      category: {
        name: {
          contains: category,
          mode: "insensitive",
        },
      },
    };
  }

  const saleItems = await prisma.saleItem.findMany({
    where: whereClause,
    include: {
      product: {
        include: {
          category: true,
        },
      },
      sale: true,
    },
  });

  // Group by product
  const productStats = saleItems.reduce((acc: any, item) => {
    const productId = item.product.id;
    if (!acc[productId]) {
      acc[productId] = {
        product: item.product,
        totalQuantity: 0,
        totalRevenue: 0,
        totalTransactions: 0,
        averagePrice: 0,
      };
    }
    acc[productId].totalQuantity += item.quantity;
    acc[productId].totalRevenue += item.total;
    acc[productId].totalTransactions++;
    return acc;
  }, {});

  // Calculate average price and sort by revenue
  const topProducts = Object.values(productStats)
    .map((stats: any) => ({
      ...stats,
      averagePrice: stats.totalRevenue / stats.totalQuantity,
    }))
    .sort((a: any, b: any) => b.totalRevenue - a.totalRevenue);

  return {
    topSellingProducts: topProducts.slice(0, 20),
    totalProductsSold: Object.keys(productStats).length,
    totalQuantitySold: saleItems.reduce((sum, item) => sum + item.quantity, 0),
    totalRevenue: saleItems.reduce((sum, item) => sum + item.total, 0),
  };
}

async function getCustomersReport(startDate: Date, endDate: Date) {
  const customers = await prisma.customer.findMany({
    include: {
      sales: {
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: "COMPLETED",
        },
        include: {
          saleItems: true,
        },
      },
    },
  });

  const customerStats = customers
    .map(customer => {
      const totalSpent = customer.sales.reduce((sum, sale) => sum + sale.total, 0);
      const totalVisits = customer.sales.length;
      const averageOrderValue = totalVisits > 0 ? totalSpent / totalVisits : 0;
      const lastVisit = customer.sales.length > 0 
        ? customer.sales.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0].createdAt
        : null;

      return {
        ...customer,
        totalSpent,
        totalVisits,
        averageOrderValue,
        lastVisit,
      };
    })
    .filter(customer => customer.totalSpent > 0)
    .sort((a, b) => b.totalSpent - a.totalSpent);

  return {
    topCustomers: customerStats.slice(0, 20),
    totalCustomers: customerStats.length,
    totalRevenue: customerStats.reduce((sum, customer) => sum + customer.totalSpent, 0),
    averageCustomerValue: customerStats.length > 0 
      ? customerStats.reduce((sum, customer) => sum + customer.totalSpent, 0) / customerStats.length 
      : 0,
  };
}

async function getFinancialReport(startDate: Date, endDate: Date) {
  const sales = await prisma.sale.findMany({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: "COMPLETED",
    },
    include: {
      saleItems: {
        include: {
          product: true,
        },
      },
    },
  });

  const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);
  const totalTax = sales.reduce((sum, sale) => sum + (sale.tax || 0), 0);
  const totalDiscount = sales.reduce((sum, sale) => sum + (sale.discount || 0), 0);
  
  // Calculate cost of goods sold
  const totalCost = sales.reduce((sum, sale) => {
    return sum + sale.saleItems.reduce((itemSum, item) => {
      return itemSum + (item.product.cost || 0) * item.quantity;
    }, 0);
  }, 0);

  const grossProfit = totalRevenue - totalCost;
  const grossProfitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

  return {
    totalRevenue,
    totalCost,
    grossProfit,
    grossProfitMargin,
    totalTax,
    totalDiscount,
    netRevenue: totalRevenue - totalDiscount,
    totalTransactions: sales.length,
  };
}

async function getTaxReport(startDate: Date, endDate: Date) {
  const sales = await prisma.sale.findMany({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: "COMPLETED",
    },
    orderBy: { createdAt: "desc" },
  });

  const totalTaxCollected = sales.reduce((sum, sale) => sum + (sale.tax || 0), 0);
  const totalTaxableAmount = sales.reduce((sum, sale) => sum + sale.total, 0);
  const averageTaxRate = totalTaxableAmount > 0 ? (totalTaxCollected / totalTaxableAmount) * 100 : 0;

  // Group by date for daily tax collection
  const dailyTax = sales.reduce((acc: any, sale) => {
    const date = sale.createdAt.toISOString().split('T')[0];
    if (!acc[date]) {
      acc[date] = { date, taxCollected: 0, taxableAmount: 0 };
    }
    acc[date].taxCollected += sale.tax || 0;
    acc[date].taxableAmount += sale.total;
    return acc;
  }, {});

  return {
    summary: {
      totalTaxCollected,
      totalTaxableAmount,
      averageTaxRate,
      totalTransactions: sales.length,
    },
    dailyTax: Object.values(dailyTax),
    recentTransactions: sales.slice(0, 20),
  };
}
