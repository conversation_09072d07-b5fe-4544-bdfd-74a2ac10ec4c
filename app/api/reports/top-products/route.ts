import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const days = parseInt(searchParams.get("days") || "30");

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    startDate.setHours(0, 0, 0, 0);

    // Get sale items for the specified period
    const saleItems = await prisma.saleItem.findMany({
      where: {
        sale: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: "COMPLETED",
        },
      },
      include: {
        product: {
          include: {
            category: true,
          },
        },
        sale: true,
      },
    });

    // Group by product and calculate statistics
    const productStats = saleItems.reduce((acc: any, item) => {
      const productId = item.product.id;
      
      if (!acc[productId]) {
        acc[productId] = {
          id: item.product.id,
          name: item.product.name,
          category: item.product.category.name,
          sku: item.product.sku,
          currentPrice: item.product.price,
          units: 0,
          revenue: 0,
          transactions: new Set(),
          averagePrice: 0,
        };
      }
      
      acc[productId].units += item.quantity;
      acc[productId].revenue += item.total;
      acc[productId].transactions.add(item.sale.id);
      
      return acc;
    }, {});

    // Calculate growth comparison (previous period)
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - days);
    const previousEndDate = new Date(startDate);

    const previousSaleItems = await prisma.saleItem.findMany({
      where: {
        sale: {
          createdAt: {
            gte: previousStartDate,
            lte: previousEndDate,
          },
          status: "COMPLETED",
        },
      },
      include: {
        product: true,
      },
    });

    const previousStats = previousSaleItems.reduce((acc: any, item) => {
      const productId = item.product.id;
      if (!acc[productId]) {
        acc[productId] = { revenue: 0, units: 0 };
      }
      acc[productId].revenue += item.total;
      acc[productId].units += item.quantity;
      return acc;
    }, {});

    // Convert to array and calculate final statistics
    const topProducts = Object.values(productStats)
      .map((stats: any) => {
        const transactionCount = stats.transactions.size;
        const averagePrice = stats.units > 0 ? stats.revenue / stats.units : 0;
        
        // Calculate growth
        const previousRevenue = previousStats[stats.id]?.revenue || 0;
        const growth = previousRevenue > 0 
          ? ((stats.revenue - previousRevenue) / previousRevenue) * 100 
          : stats.revenue > 0 ? 100 : 0;

        return {
          id: stats.id,
          name: stats.name,
          category: stats.category,
          sku: stats.sku,
          currentPrice: stats.currentPrice,
          units: stats.units,
          revenue: stats.revenue,
          transactions: transactionCount,
          averagePrice,
          growth: Math.round(growth * 10) / 10, // Round to 1 decimal place
        };
      })
      .sort((a: any, b: any) => b.revenue - a.revenue) // Sort by revenue
      .slice(0, limit);

    return NextResponse.json({
      products: topProducts,
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        days,
      },
      summary: {
        totalProducts: topProducts.length,
        totalRevenue: topProducts.reduce((sum: number, product: any) => sum + product.revenue, 0),
        totalUnits: topProducts.reduce((sum: number, product: any) => sum + product.units, 0),
      },
    });
  } catch (error) {
    console.error("Error fetching top products:", error);
    return NextResponse.json(
      { error: "Failed to fetch top products" },
      { status: 500 }
    );
  }
}
