import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category") || "all";
    const status = searchParams.get("status") || "all"; // all, good, low, out
    const limit = parseInt(searchParams.get("limit") || "50");

    // Build where clause
    const whereClause: any = {
      isActive: true,
    };

    if (category !== "all") {
      whereClause.category = {
        name: {
          contains: category,
          mode: "insensitive",
        },
      };
    }

    // Get products with stock information
    const products = await prisma.product.findMany({
      where: whereClause,
      include: {
        category: true,
        stockAdjustments: {
          orderBy: { createdAt: "desc" },
          take: 1,
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: { name: "asc" },
    });

    // Calculate stock status for each product
    const productsWithStatus = products.map(product => {
      let stockStatus: "Good" | "Low" | "Out";
      
      if (product.stock === 0) {
        stockStatus = "Out";
      } else if (product.stock <= product.minStock) {
        stockStatus = "Low";
      } else {
        stockStatus = "Good";
      }

      const lastAdjustment = product.stockAdjustments[0];
      const lastRestocked = lastAdjustment 
        ? lastAdjustment.createdAt.toISOString().split('T')[0]
        : null;
      const lastRestockedBy = lastAdjustment?.user?.name || null;

      return {
        id: product.id,
        name: product.name,
        sku: product.sku,
        category: product.category.name,
        currentStock: product.stock,
        minStock: product.minStock,
        status: stockStatus,
        stockValue: product.stock * product.price,
        costValue: product.stock * (product.cost || 0),
        lastRestocked,
        lastRestockedBy,
        price: product.price,
        cost: product.cost,
      };
    });

    // Filter by status if specified
    let filteredProducts = productsWithStatus;
    if (status !== "all") {
      const statusFilter = status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
      filteredProducts = productsWithStatus.filter(product => product.status === statusFilter);
    }

    // Apply limit
    const limitedProducts = filteredProducts.slice(0, limit);

    // Calculate summary statistics
    const summary = {
      totalProducts: productsWithStatus.length,
      goodStock: productsWithStatus.filter(p => p.status === "Good").length,
      lowStock: productsWithStatus.filter(p => p.status === "Low").length,
      outOfStock: productsWithStatus.filter(p => p.status === "Out").length,
      totalStockValue: productsWithStatus.reduce((sum, p) => sum + p.stockValue, 0),
      totalCostValue: productsWithStatus.reduce((sum, p) => sum + p.costValue, 0),
    };

    // Category breakdown
    const categoryBreakdown = productsWithStatus.reduce((acc: any, product) => {
      const categoryName = product.category;
      if (!acc[categoryName]) {
        acc[categoryName] = {
          name: categoryName,
          totalProducts: 0,
          goodStock: 0,
          lowStock: 0,
          outOfStock: 0,
          totalValue: 0,
        };
      }
      
      acc[categoryName].totalProducts++;
      acc[categoryName].totalValue += product.stockValue;
      
      switch (product.status) {
        case "Good":
          acc[categoryName].goodStock++;
          break;
        case "Low":
          acc[categoryName].lowStock++;
          break;
        case "Out":
          acc[categoryName].outOfStock++;
          break;
      }
      
      return acc;
    }, {});

    // Get recent stock adjustments
    const recentAdjustments = await prisma.stockAdjustment.findMany({
      take: 10,
      orderBy: { createdAt: "desc" },
      include: {
        product: {
          select: {
            name: true,
            sku: true,
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    const adjustmentsWithDetails = recentAdjustments.map(adjustment => ({
      id: adjustment.id,
      productName: adjustment.product.name,
      productSku: adjustment.product.sku,
      type: adjustment.type,
      quantity: adjustment.quantity,
      reason: adjustment.reason,
      adjustedBy: adjustment.user.name,
      createdAt: adjustment.createdAt,
    }));

    return NextResponse.json({
      products: limitedProducts,
      summary,
      categoryBreakdown: Object.values(categoryBreakdown),
      recentAdjustments: adjustmentsWithDetails,
      filters: {
        category,
        status,
        limit,
      },
    });
  } catch (error) {
    console.error("Error fetching inventory status:", error);
    return NextResponse.json(
      { error: "Failed to fetch inventory status" },
      { status: 500 }
    );
  }
}
