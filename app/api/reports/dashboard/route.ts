import { NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET() {
  try {
    const now = new Date();
    
    // Today's sales
    const todayStart = new Date(now);
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date(now);
    todayEnd.setHours(23, 59, 59, 999);

    // This week's sales
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
    weekStart.setHours(0, 0, 0, 0);

    // This month's sales
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // This year's sales
    const yearStart = new Date(now.getFullYear(), 0, 1);

    // Yesterday's sales for comparison
    const yesterdayStart = new Date(now);
    yesterdayStart.setDate(now.getDate() - 1);
    yesterdayStart.setHours(0, 0, 0, 0);
    const yesterdayEnd = new Date(yesterdayStart);
    yesterdayEnd.setHours(23, 59, 59, 999);

    // Last week's sales for comparison
    const lastWeekStart = new Date(weekStart);
    lastWeekStart.setDate(lastWeekStart.getDate() - 7);
    const lastWeekEnd = new Date(weekStart);
    lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
    lastWeekEnd.setHours(23, 59, 59, 999);

    // Last month's sales for comparison
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
    lastMonthEnd.setHours(23, 59, 59, 999);

    // Last year's sales for comparison
    const lastYearStart = new Date(now.getFullYear() - 1, 0, 1);
    const lastYearEnd = new Date(now.getFullYear() - 1, 11, 31);
    lastYearEnd.setHours(23, 59, 59, 999);

    // Fetch all sales data in parallel
    const [
      todaySales,
      weekSales,
      monthSales,
      yearSales,
      yesterdaySales,
      lastWeekSales,
      lastMonthSales,
      lastYearSales,
    ] = await Promise.all([
      // Today's sales
      prisma.sale.aggregate({
        where: {
          createdAt: { gte: todayStart, lte: todayEnd },
          status: "COMPLETED",
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // This week's sales
      prisma.sale.aggregate({
        where: {
          createdAt: { gte: weekStart },
          status: "COMPLETED",
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // This month's sales
      prisma.sale.aggregate({
        where: {
          createdAt: { gte: monthStart },
          status: "COMPLETED",
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // This year's sales
      prisma.sale.aggregate({
        where: {
          createdAt: { gte: yearStart },
          status: "COMPLETED",
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // Yesterday's sales
      prisma.sale.aggregate({
        where: {
          createdAt: { gte: yesterdayStart, lte: yesterdayEnd },
          status: "COMPLETED",
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // Last week's sales
      prisma.sale.aggregate({
        where: {
          createdAt: { gte: lastWeekStart, lte: lastWeekEnd },
          status: "COMPLETED",
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // Last month's sales
      prisma.sale.aggregate({
        where: {
          createdAt: { gte: lastMonthStart, lte: lastMonthEnd },
          status: "COMPLETED",
        },
        _sum: { total: true },
        _count: true,
      }),
      
      // Last year's sales
      prisma.sale.aggregate({
        where: {
          createdAt: { gte: lastYearStart, lte: lastYearEnd },
          status: "COMPLETED",
        },
        _sum: { total: true },
        _count: true,
      }),
    ]);

    // Calculate growth percentages
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const todayTotal = todaySales._sum.total || 0;
    const weekTotal = weekSales._sum.total || 0;
    const monthTotal = monthSales._sum.total || 0;
    const yearTotal = yearSales._sum.total || 0;

    const yesterdayTotal = yesterdaySales._sum.total || 0;
    const lastWeekTotal = lastWeekSales._sum.total || 0;
    const lastMonthTotal = lastMonthSales._sum.total || 0;
    const lastYearTotal = lastYearSales._sum.total || 0;

    // Get additional stats
    const [lowStockCount, totalProducts, totalCustomers] = await Promise.all([
      // Low stock products count
      prisma.product.count({
        where: {
          isActive: true,
          stock: { lte: prisma.product.fields.minStock },
        },
      }),
      
      // Total active products
      prisma.product.count({
        where: { isActive: true },
      }),
      
      // Total customers
      prisma.customer.count(),
    ]);

    return NextResponse.json({
      sales: {
        today: {
          total: todayTotal,
          count: todaySales._count,
          growth: calculateGrowth(todayTotal, yesterdayTotal),
        },
        week: {
          total: weekTotal,
          count: weekSales._count,
          growth: calculateGrowth(weekTotal, lastWeekTotal),
        },
        month: {
          total: monthTotal,
          count: monthSales._count,
          growth: calculateGrowth(monthTotal, lastMonthTotal),
        },
        year: {
          total: yearTotal,
          count: yearSales._count,
          growth: calculateGrowth(yearTotal, lastYearTotal),
        },
      },
      inventory: {
        totalProducts,
        lowStockCount,
        lowStockPercentage: totalProducts > 0 ? (lowStockCount / totalProducts) * 100 : 0,
      },
      customers: {
        total: totalCustomers,
      },
    });
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch dashboard statistics" },
      { status: 500 }
    );
  }
}
