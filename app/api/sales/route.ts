import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const page = parseInt(searchParams.get("page") || "1");
    const status = searchParams.get("status") || "all";
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    // Build where clause
    const whereClause: any = {};

    if (status !== "all") {
      whereClause.status = status.toUpperCase();
    }

    if (startDate && endDate) {
      whereClause.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Fetch sales with related data
    const [sales, totalCount] = await Promise.all([
      prisma.sale.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  price: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: offset,
        take: limit,
      }),
      
      prisma.sale.count({
        where: whereClause,
      }),
    ]);

    // Transform the data to include item count and other calculated fields
    const salesWithDetails = sales.map(sale => ({
      id: sale.id,
      total: sale.total,
      subtotal: sale.subtotal,
      tax: sale.tax,
      discount: sale.discount,
      status: sale.status,
      paymentType: sale.paymentType,
      createdAt: sale.createdAt,
      updatedAt: sale.updatedAt,
      customer: sale.customer,
      user: sale.user,
      itemCount: sale.saleItems.reduce((sum, item) => sum + item.quantity, 0),
      items: sale.saleItems.map(item => ({
        id: item.id,
        quantity: item.quantity,
        price: item.price,
        total: item.total,
        product: item.product,
      })),
    }));

    // Calculate summary statistics
    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalTax = sales.reduce((sum, sale) => sum + (sale.tax || 0), 0);
    const totalDiscount = sales.reduce((sum, sale) => sum + (sale.discount || 0), 0);
    const averageOrderValue = sales.length > 0 ? totalSales / sales.length : 0;

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      sales: salesWithDetails,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
        hasNextPage,
        hasPreviousPage,
      },
      summary: {
        totalSales,
        totalTax,
        totalDiscount,
        averageOrderValue,
        totalTransactions: sales.length,
      },
    });
  } catch (error) {
    console.error("Error fetching sales:", error);
    return NextResponse.json(
      { error: "Failed to fetch sales data" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      customerId,
      userId,
      items,
      subtotal,
      tax,
      discount,
      total,
      paymentType,
      notes,
    } = body;

    // Validate required fields
    if (!userId || !items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: "Missing required fields: userId and items are required" },
        { status: 400 }
      );
    }

    if (!total || total <= 0) {
      return NextResponse.json(
        { error: "Total must be greater than 0" },
        { status: 400 }
      );
    }

    // Create the sale with items in a transaction
    const sale = await prisma.$transaction(async (tx) => {
      // Create the sale
      const newSale = await tx.sale.create({
        data: {
          customerId: customerId || null,
          userId,
          subtotal: subtotal || 0,
          tax: tax || 0,
          discount: discount || 0,
          total,
          paymentType: paymentType || "CASH",
          status: "COMPLETED",
          notes: notes || null,
        },
      });

      // Create sale items and update product stock
      for (const item of items) {
        const { productId, quantity, price } = item;
        
        if (!productId || !quantity || quantity <= 0 || !price || price <= 0) {
          throw new Error("Invalid item data: productId, quantity, and price are required");
        }

        // Create sale item
        await tx.saleItem.create({
          data: {
            saleId: newSale.id,
            productId,
            quantity,
            price,
            total: quantity * price,
          },
        });

        // Update product stock
        await tx.product.update({
          where: { id: productId },
          data: {
            stock: {
              decrement: quantity,
            },
          },
        });
      }

      return newSale;
    });

    // Fetch the complete sale with related data
    const completeSale = await prisma.sale.findUnique({
      where: { id: sale.id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        saleItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                price: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      sale: completeSale,
      message: "Sale created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating sale:", error);
    return NextResponse.json(
      { error: "Failed to create sale" },
      { status: 500 }
    );
  }
}
