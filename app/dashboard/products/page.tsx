"use client";

import {
    <PERSON><PERSON>,
    FormCard,
    FormGrid,
    SearchInput,
    Select
} from "@/lib/forms";
import Link from "next/link";
import { useState } from "react";

// Mock product data
const mockProducts = [
  {
    id: 1,
    name: "Organic Bananas",
    category: "fruits",
    price: 2.99,
    costPrice: 1.50,
    stock: 150,
    sku: "ORG-BAN-001",
    status: "active",
    isActive: true,
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    name: "Whole Milk",
    category: "dairy",
    price: 3.49,
    costPrice: 2.20,
    stock: 45,
    sku: "MILK-WHL-001",
    status: "active",
    isActive: true,
    createdAt: "2024-01-14",
  },
  {
    id: 3,
    name: "Bread - Whole Wheat",
    category: "bakery",
    price: 2.79,
    costPrice: 1.80,
    stock: 8,
    sku: "BRD-WHT-001",
    status: "low-stock",
    isActive: true,
    createdAt: "2024-01-13",
  },
  {
    id: 4,
    name: "Ground Coffee",
    category: "beverages",
    price: 12.99,
    costPrice: 8.50,
    stock: 0,
    sku: "COF-GRD-001",
    status: "out-of-stock",
    isActive: false,
    createdAt: "2024-01-12",
  },
  {
    id: 5,
    name: "Greek Yogurt",
    category: "dairy",
    price: 4.99,
    costPrice: 3.20,
    stock: 75,
    sku: "YOG-GRK-001",
    status: "active",
    isActive: true,
    createdAt: "2024-01-11",
  },
];

export default function ProductsPage() {
  const [products, setProducts] = useState(mockProducts);
  const [filters, setFilters] = useState({
    search: "",
    category: "",
    status: "",
  });

  // Filter options
  const categoryOptions = [
    { value: "", label: "All Categories" },
    { value: "fruits", label: "Fruits" },
    { value: "vegetables", label: "Vegetables" },
    { value: "dairy", label: "Dairy" },
    { value: "bakery", label: "Bakery" },
    { value: "beverages", label: "Beverages" },
    { value: "meat", label: "Meat & Poultry" },
    { value: "frozen", label: "Frozen Foods" },
  ];

  const statusOptions = [
    { value: "", label: "All Status" },
    { value: "active", label: "In Stock" },
    { value: "low-stock", label: "Low Stock" },
    { value: "out-of-stock", label: "Out of Stock" },
  ];

  // Filter products based on search and filters
  const filteredProducts = products.filter((product) => {
    const matchesSearch =
      product.name.toLowerCase().includes(filters.search.toLowerCase()) ||
      product.sku.toLowerCase().includes(filters.search.toLowerCase());

    const matchesCategory = !filters.category || product.category === filters.category;
    const matchesStatus = !filters.status || product.status === filters.status;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, search: query }));
  };

  const handleDeleteProduct = (productId: number) => {
    if (confirm("Are you sure you want to delete this product?")) {
      setProducts(prev => prev.filter(p => p.id !== productId));
    }
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100",
      "low-stock": "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100",
      "out-of-stock": "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100",
    };

    const statusLabels = {
      active: "In Stock",
      "low-stock": "Low Stock",
      "out-of-stock": "Out of Stock",
    };

    return (
      <span
        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          statusClasses[status as keyof typeof statusClasses]
        }`}
      >
        {statusLabels[status as keyof typeof statusLabels]}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Products
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Manage your product inventory and pricing
            </p>
          </div>
          <Link
            href="/dashboard/products/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Product
          </Link>
        </div>
      </div>

      {/* Search and Filters */}
      <FormCard>
        <FormGrid cols={4} gap="md">
          <SearchInput
            name="search"
            placeholder="Search products by name or SKU..."
            onSearch={handleSearch}
            debounceMs={300}
            showClearButton
          />

          <Select
            name="category"
            placeholder="Category"
            options={categoryOptions}
            value={filters.category}
            onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
          />

          <Select
            name="status"
            placeholder="Status"
            options={statusOptions}
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
          />

          <Button
            variant="ghost"
            onClick={() => setFilters({ search: "", category: "", status: "" })}
            className="w-full"
          >
            Clear Filters
          </Button>
        </FormGrid>
      </FormCard>

      {/* Products Table */}
      <FormCard>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
              {filteredProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {product.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        SKU: {product.sku}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white capitalize">
                    {product.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ${product.price.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {product.stock}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(product.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Link
                      href={`/dashboard/products/${product.id}`}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      Edit
                    </Link>
                    <button
                      onClick={() => handleDeleteProduct(product.id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No products found
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Try adjusting your search or filter criteria.
            </p>
          </div>
        )}
      </FormCard>
    </div>
  );
}
