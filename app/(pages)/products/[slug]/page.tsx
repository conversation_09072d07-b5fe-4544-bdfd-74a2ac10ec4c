"use client";

import {
  CancelButton,
  CategorySelect,
  Checkbox,
  CurrencyInput,
  DeleteButton,
  FormActions,
  FormCard,
  FormGrid,
  FormSection,
  Input,
  NumberInput,
  SubmitButton,
  Textarea,
} from "@/lib/forms";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function ProductDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const [product, setProduct] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    price: 0,
    stock: 0,
    sku: "",
    description: "",
    barcode: "",
    supplier: "",
    costPrice: 0,
    weight: "",
    expiryDate: "",
    isActive: true,
    isFeatured: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load product data
  useEffect(() => {
    const loadProduct = async () => {
      try {
        const resolvedParams = await params;
        const response = await fetch(`/api/products/${resolvedParams.slug}`);
        if (!response.ok) {
          throw new Error("Product not found");
        }
        const productData = await response.json();
        setProduct(productData);
        setFormData({
          name: productData.name,
          category: productData.categoryId.toString(),
          price: productData.price,
          stock: productData.stock,
          sku: productData.sku || "",
          description: productData.description || "",
          barcode: productData.barcode || "",
          supplier: "", // This field might not exist in the API
          costPrice: productData.cost || 0,
          weight: "", // This field might not exist in the API
          expiryDate: "", // This field might not exist in the API
          isActive: productData.isActive,
          isFeatured: false, // This field might not exist in the API
        });
      } catch (error) {
        console.error("Error loading product:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProduct();
  }, [params]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    try {
      const resolvedParams = await params;
      const response = await fetch(`/api/products/${resolvedParams.slug}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          barcode: formData.barcode,
          sku: formData.sku,
          price: formData.price,
          cost: formData.costPrice,
          stock: formData.stock,
          categoryId: parseInt(formData.category),
          isActive: formData.isActive,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.fieldErrors) {
          setErrors(errorData.fieldErrors);
        } else {
          setErrors({ general: errorData.error || "Failed to update product" });
        }
        return;
      }

      // Success - redirect to products list
      window.location.href = "/products";
    } catch (error) {
      setErrors({ general: "Failed to update product" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleDelete = async () => {
    if (confirm("Are you sure you want to delete this product?")) {
      try {
        const resolvedParams = await params;
        const response = await fetch(`/api/products/${resolvedParams.slug}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to delete product");
        }

        // Redirect to products list
        window.location.href = "/products";
      } catch (error) {
        console.error("Error deleting product:", error);
        setErrors({ general: "Failed to delete product" });
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="py-12 text-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Product not found
        </h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          The product you're looking for doesn't exist.
        </p>
        <Link
          href="/products"
          className="mt-4 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-600 hover:bg-blue-200"
        >
          Back to Products
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/products"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Products
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="h-5 w-5 flex-shrink-0 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      {product.name}
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Edit Product
            </h1>
          </div>
        </div>
      </div>

      {/* Product Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {errors.general && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors.general}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Product Information */}
          <div className="lg:col-span-2">
            <FormSection
              title="Product Information"
              description="Basic product details and description"
            >
              <FormGrid cols={2} gap="md">
                <div className="sm:col-span-2">
                  <Input
                    name="name"
                    label="Product Name"
                    placeholder="Enter product name"
                    required
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    error={errors.name}
                  />
                </div>

                <Input
                  name="sku"
                  label="SKU"
                  placeholder="Product SKU"
                  required
                  value={formData.sku}
                  onChange={(e) => handleInputChange("sku", e.target.value)}
                  error={errors.sku}
                />

                <Input
                  name="barcode"
                  label="Barcode"
                  placeholder="Product barcode"
                  value={formData.barcode}
                  onChange={(e) => handleInputChange("barcode", e.target.value)}
                  error={errors.barcode}
                />

                <CategorySelect
                  name="category"
                  label="Category"
                  required
                  value={formData.category}
                  onChange={(e) =>
                    handleInputChange("category", e.target.value)
                  }
                  error={errors.category}
                />

                <Input
                  name="supplier"
                  label="Supplier"
                  placeholder="Supplier name"
                  value={formData.supplier}
                  onChange={(e) =>
                    handleInputChange("supplier", e.target.value)
                  }
                  error={errors.supplier}
                />
              </FormGrid>

              <Textarea
                name="description"
                label="Description"
                placeholder="Product description"
                rows={3}
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                error={errors.description}
                helpText="Provide a detailed description of the product"
              />
            </FormSection>
          </div>

          {/* Pricing and Inventory */}
          <div className="space-y-8">
            <FormCard title="Pricing" description="Set product pricing">
              <div className="space-y-4">
                <CurrencyInput
                  name="costPrice"
                  label="Cost Price"
                  required
                  value={formData.costPrice}
                  onChange={(value) =>
                    handleInputChange("costPrice", value || 0)
                  }
                  error={errors.costPrice}
                  helpText="Your cost for this product"
                />

                <CurrencyInput
                  name="price"
                  label="Selling Price"
                  required
                  value={formData.price}
                  onChange={(value) => handleInputChange("price", value || 0)}
                  error={errors.price}
                  helpText="Customer price"
                />
              </div>
            </FormCard>

            <FormCard title="Inventory" description="Stock and product details">
              <div className="space-y-4">
                <NumberInput
                  name="stock"
                  label="Current Stock"
                  required
                  min={0}
                  value={formData.stock.toString()}
                  onChange={(e) =>
                    handleInputChange("stock", parseInt(e.target.value) || 0)
                  }
                  error={errors.stock}
                />

                <Input
                  name="weight"
                  label="Weight/Size"
                  placeholder="e.g., 1 lb, 500g"
                  value={formData.weight}
                  onChange={(e) => handleInputChange("weight", e.target.value)}
                  error={errors.weight}
                />

                <Input
                  name="expiryDate"
                  label="Expiry Date"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) =>
                    handleInputChange("expiryDate", e.target.value)
                  }
                  error={errors.expiryDate}
                />
              </div>
            </FormCard>

            <FormCard
              title="Settings"
              description="Product visibility and status"
            >
              <div className="space-y-4">
                <Checkbox
                  name="isActive"
                  label="Active Product"
                  checked={formData.isActive}
                  onChange={(e) =>
                    handleInputChange("isActive", e.target.checked)
                  }
                  helpText="Product is available for sale"
                />

                <Checkbox
                  name="isFeatured"
                  label="Featured Product"
                  checked={formData.isFeatured}
                  onChange={(e) =>
                    handleInputChange("isFeatured", e.target.checked)
                  }
                  helpText="Show in featured products section"
                />
              </div>
            </FormCard>
          </div>
        </div>

        <FormActions align="between">
          <DeleteButton type="button" onClick={handleDelete}>
            Delete Product
          </DeleteButton>
          <div className="flex space-x-3">
            <Link href="/products">
              <CancelButton>Cancel</CancelButton>
            </Link>
            <SubmitButton loading={isSubmitting} disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </SubmitButton>
          </div>
        </FormActions>
      </form>
    </div>
  );
}
