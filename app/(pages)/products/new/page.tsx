"use client";

import { ProductFormSchema, type ProductForm } from "@/lib/dto";
import { useValidatedForm } from "@/lib/form-validation";
import {
    CancelButton,
    CategorySelect,
    Checkbox,
    CurrencyInput,
    FormActions,
    FormCard,
    FormGrid,
    FormSection,
    Input,
    NumberInput,
    SubmitButton,
    Textarea,
} from "@/lib/forms";
import Link from "next/link";

export default function NewProductPage() {
  const {
    formData,
    errors,
    isSubmitting,
    updateField,
    validateField,
    submitForm,
    resetForm,
  } = useValidatedForm(ProductFormSchema, {
    name: "",
    categoryId: "",
    price: "",
    stock: "0",
    sku: "",
    description: "",
    barcode: "",
    cost: "",
    minStock: "0",
    isActive: true,
  });

  const handleSubmit = async (data: ProductForm) => {
    try {
      // Send to API
      const response = await fetch("/api/products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create product");
      }

      const product = await response.json();
      console.log("Product created:", product);

      // Handle success (redirect to products list or show success message)
      // router.push('/products');
      alert("Product created successfully!");
      resetForm();
    } catch (error) {
      console.error("Error creating product:", error);
      throw error; // Let the form handle the error
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/products"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Products
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="h-5 w-5 flex-shrink-0 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      New Product
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Add New Product
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Create a new product for your inventory
            </p>
          </div>
        </div>
      </div>

      {/* Product Form */}
      <form
        onSubmit={(e) => {
          e.preventDefault();
          submitForm(handleSubmit);
        }}
        className="space-y-8"
      >
        {errors._form && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors._form}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Product Information */}
          <div className="lg:col-span-2">
            <FormSection
              title="Product Information"
              description="Basic product details and description"
            >
              <FormGrid cols={2} gap="md">
                <div className="sm:col-span-2">
                  <Input
                    name="name"
                    label="Product Name"
                    placeholder="Enter product name"
                    required
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    error={errors.name}
                  />
                </div>

                <Input
                  name="sku"
                  label="SKU"
                  placeholder="Product SKU"
                  required
                  value={formData.sku}
                  onChange={(e) => handleInputChange("sku", e.target.value)}
                  error={errors.sku}
                  helpText="Unique product identifier"
                />

                <Input
                  name="barcode"
                  label="Barcode"
                  placeholder="Product barcode"
                  value={formData.barcode}
                  onChange={(e) => handleInputChange("barcode", e.target.value)}
                  error={errors.barcode}
                />

                <CategorySelect
                  name="category"
                  label="Category"
                  required
                  value={formData.category}
                  onChange={(e) =>
                    handleInputChange("category", e.target.value)
                  }
                  error={errors.category}
                />

                <Input
                  name="supplier"
                  label="Supplier"
                  placeholder="Supplier name"
                  value={formData.supplier}
                  onChange={(e) =>
                    handleInputChange("supplier", e.target.value)
                  }
                  error={errors.supplier}
                />
              </FormGrid>

              <Textarea
                name="description"
                label="Description"
                placeholder="Product description"
                rows={3}
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                error={errors.description}
                helpText="Provide a detailed description of the product"
              />
            </FormSection>
          </div>

          {/* Pricing and Inventory */}
          <div className="space-y-8">
            <FormCard title="Pricing" description="Set product pricing">
              <div className="space-y-4">
                <CurrencyInput
                  name="costPrice"
                  label="Cost Price"
                  required
                  value={formData.costPrice}
                  onChange={(value) =>
                    handleInputChange("costPrice", value || 0)
                  }
                  error={errors.costPrice}
                  helpText="Your cost for this product"
                />

                <CurrencyInput
                  name="price"
                  label="Selling Price"
                  required
                  value={formData.price}
                  onChange={(value) => handleInputChange("price", value || 0)}
                  error={errors.price}
                  helpText="Customer price"
                />
              </div>
            </FormCard>

            <FormCard title="Inventory" description="Stock and product details">
              <div className="space-y-4">
                <NumberInput
                  name="stock"
                  label="Initial Stock"
                  required
                  min={0}
                  value={formData.stock.toString()}
                  onChange={(e) =>
                    handleInputChange("stock", parseInt(e.target.value) || 0)
                  }
                  error={errors.stock}
                  helpText="Starting inventory quantity"
                />

                <Input
                  name="weight"
                  label="Weight/Size"
                  placeholder="e.g., 1 lb, 500g"
                  value={formData.weight}
                  onChange={(e) => handleInputChange("weight", e.target.value)}
                  error={errors.weight}
                />

                <Input
                  name="expiryDate"
                  label="Expiry Date"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) =>
                    handleInputChange("expiryDate", e.target.value)
                  }
                  error={errors.expiryDate}
                  helpText="Leave empty if product doesn't expire"
                />
              </div>
            </FormCard>

            <FormCard
              title="Settings"
              description="Product visibility and status"
            >
              <div className="space-y-4">
                <Checkbox
                  name="isActive"
                  label="Active Product"
                  checked={formData.isActive}
                  onChange={(e) =>
                    handleInputChange("isActive", e.target.checked)
                  }
                  helpText="Product is available for sale"
                />

                <Checkbox
                  name="isFeatured"
                  label="Featured Product"
                  checked={formData.isFeatured}
                  onChange={(e) =>
                    handleInputChange("isFeatured", e.target.checked)
                  }
                  helpText="Show in featured products section"
                />
              </div>
            </FormCard>
          </div>
        </div>

        <FormActions align="right">
          <div className="flex space-x-3">
            <Link href="/products">
              <CancelButton>Cancel</CancelButton>
            </Link>
            <SubmitButton loading={isSubmitting} disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Product"}
            </SubmitButton>
          </div>
        </FormActions>
      </form>
    </div>
  );
}
