"use client";

import {
    <PERSON>ton,
    CancelButton,
    FormActions,
    FormCard,
    FormGrid,
    Input,
    NumberInput,
    SearchInput,
    Select,
    SubmitButton,
    Textarea,
} from "@/lib/forms";
import Link from "next/link";
import { useEffect, useState } from "react";

// Count-up animation hook
function useCountUp(end: number, duration: number = 2000) {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration]);

  return count;
}

// Mock adjustments data
const adjustments = [
  {
    id: 1,
    productName: "Organic Bananas",
    sku: "ORG-BAN-001",
    type: "increase",
    quantity: 25,
    reason: "Stock count correction",
    user: "<PERSON> Smith",
    date: "2024-01-15",
    time: "14:30",
    notes: "Physical count revealed discrepancy",
  },
  {
    id: 2,
    productName: "Whole Milk",
    sku: "MILK-WHL-001",
    type: "decrease",
    quantity: 10,
    reason: "Damaged goods",
    user: "Alice Johnson",
    date: "2024-01-15",
    time: "12:15",
    notes: "Expired products removed from shelf",
  },
  {
    id: 3,
    productName: "Ground Coffee",
    sku: "COF-GRD-001",
    type: "increase",
    quantity: 15,
    reason: "Physical count adjustment",
    user: "Bob Wilson",
    date: "2024-01-15",
    time: "10:45",
    notes: "Reconciliation after inventory audit",
  },
];

export default function StockAdjustmentsPage() {
  const [filters, setFilters] = useState({
    search: "",
    type: "",
    reason: "",
    dateFrom: "",
    dateTo: "",
  });

  const [showNewAdjustment, setShowNewAdjustment] = useState(false);
  const [newAdjustment, setNewAdjustment] = useState({
    productSearch: "",
    selectedProduct: null as any,
    type: "increase",
    quantity: "",
    reason: "",
    notes: "",
  });

  // Count-up animations for stats
  const totalAdjustments = useCountUp(adjustments.length);
  const increasedItems = useCountUp(
    adjustments.filter((adj) => adj.type === "increase").length,
  );
  const decreasedItems = useCountUp(
    adjustments.filter((adj) => adj.type === "decrease").length,
  );
  const totalQuantityChanged = useCountUp(
    adjustments.reduce((sum, adj) => sum + adj.quantity, 0),
  );

  const typeOptions = [
    { value: "", label: "All Types" },
    { value: "increase", label: "Stock Increase" },
    { value: "decrease", label: "Stock Decrease" },
  ];

  const reasonOptions = [
    { value: "", label: "All Reasons" },
    { value: "damaged", label: "Damaged Goods" },
    { value: "expired", label: "Expired Products" },
    { value: "theft", label: "Theft/Loss" },
    { value: "count-correction", label: "Count Correction" },
    { value: "return", label: "Customer Return" },
    { value: "transfer", label: "Store Transfer" },
    { value: "other", label: "Other" },
  ];

  const adjustmentTypeOptions = [
    { value: "increase", label: "Increase Stock" },
    { value: "decrease", label: "Decrease Stock" },
  ];

  const handleSearch = (query: string) => {
    setFilters((prev) => ({ ...prev, search: query }));
  };

  const handleSubmitAdjustment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !newAdjustment.selectedProduct ||
      !newAdjustment.quantity ||
      !newAdjustment.reason
    ) {
      alert("Please fill in all required fields");
      return;
    }

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      console.log("Stock adjustment submitted:", newAdjustment);

      // Reset form
      setNewAdjustment({
        productSearch: "",
        selectedProduct: null,
        type: "increase",
        quantity: "",
        reason: "",
        notes: "",
      });
      setShowNewAdjustment(false);

      alert("Stock adjustment recorded successfully!");
    } catch (error) {
      console.error("Error submitting adjustment:", error);
      alert("Error recording adjustment. Please try again.");
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/inventory"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Inventory
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="h-5 w-5 flex-shrink-0 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      Stock Adjustments
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Stock Adjustments
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Adjust stock levels and track inventory changes
            </p>
          </div>
          <div className="flex space-x-3">
            <Link href="/inventory/adjustments/bulk">
              <Button variant="secondary">Bulk Adjustment</Button>
            </Link>
            <Button
              variant="primary"
              onClick={() => setShowNewAdjustment(true)}
            >
              New Adjustment
            </Button>
          </div>
        </div>
      </div>

      {/* Adjustment Statistics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-md bg-blue-500">
                  <svg
                    className="h-5 w-5 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Total Adjustments
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {totalAdjustments}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-md bg-green-500">
                  <svg
                    className="h-5 w-5 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Stock Increases
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {increasedItems}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-md bg-red-500">
                  <svg
                    className="h-5 w-5 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M20 12H4"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Stock Decreases
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {decreasedItems}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-md bg-purple-500">
                  <svg
                    className="h-5 w-5 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Total Quantity
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {totalQuantityChanged}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <FormCard title="Search & Filter Adjustments">
        <FormGrid cols={4} gap="md">
          <SearchInput
            name="search"
            label="Search Adjustments"
            placeholder="Search by product name or SKU..."
            value={filters.search}
            onSearch={handleSearch}
            debounceMs={300}
            showClearButton
          />

          <Select
            name="type"
            label="Adjustment Type"
            options={typeOptions}
            value={filters.type}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, type: e.target.value }))
            }
          />

          <Select
            name="reason"
            label="Reason"
            options={reasonOptions}
            value={filters.reason}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, reason: e.target.value }))
            }
          />

          <Input
            name="dateFrom"
            label="From Date"
            type="date"
            value={filters.dateFrom}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, dateFrom: e.target.value }))
            }
          />

          <Input
            name="dateTo"
            label="To Date"
            type="date"
            value={filters.dateTo}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, dateTo: e.target.value }))
            }
          />
        </FormGrid>
      </FormCard>

      {/* Adjustments Table */}
      <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Quantity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Reason
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Date & Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {adjustments.map((adjustment) => (
                <tr
                  key={adjustment.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {adjustment.productName}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        SKU: {adjustment.sku}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {adjustment.type === "increase" ? (
                        <div className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-green-500">
                          <svg
                            className="h-4 w-4 text-white"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                            />
                          </svg>
                        </div>
                      ) : (
                        <div className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500">
                          <svg
                            className="h-4 w-4 text-white"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M20 12H4"
                            />
                          </svg>
                        </div>
                      )}
                      <span className="text-sm text-gray-900 capitalize dark:text-white">
                        {adjustment.type}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900 dark:text-white">
                    {adjustment.type === "increase" ? "+" : "-"}
                    {adjustment.quantity}
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                    {adjustment.reason}
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                    {adjustment.user}
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                    <div>
                      <div>{adjustment.date}</div>
                      <div className="text-gray-500 dark:text-gray-400">
                        {adjustment.time}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                    <button className="mr-4 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                      View Details
                    </button>
                    <button className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                      Reverse
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* New Adjustment Modal */}
      {showNewAdjustment && (
        <div className="bg-opacity-50 fixed inset-0 z-50 h-full w-full overflow-y-auto bg-gray-600">
          <div className="relative top-20 mx-auto w-11/12 rounded-md border bg-white p-5 shadow-lg md:w-3/4 lg:w-1/2 dark:bg-gray-800">
            <div className="mt-3">
              <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
                New Stock Adjustment
              </h3>

              <form onSubmit={handleSubmitAdjustment} className="space-y-6">
                <FormGrid cols={1} gap="md">
                  <SearchInput
                    name="productSearch"
                    label="Search Product"
                    placeholder="Search by name or SKU..."
                    value={newAdjustment.productSearch}
                    onSearch={(query) =>
                      setNewAdjustment((prev) => ({
                        ...prev,
                        productSearch: query,
                      }))
                    }
                    required
                  />

                  <Select
                    name="adjustmentType"
                    label="Adjustment Type"
                    options={adjustmentTypeOptions}
                    value={newAdjustment.type}
                    onChange={(e) =>
                      setNewAdjustment((prev) => ({
                        ...prev,
                        type: e.target.value,
                      }))
                    }
                    required
                  />

                  <NumberInput
                    name="quantity"
                    label="Quantity"
                    placeholder="Enter quantity to adjust"
                    value={newAdjustment.quantity}
                    onChange={(e) =>
                      setNewAdjustment((prev) => ({
                        ...prev,
                        quantity: e.target.value,
                      }))
                    }
                    min={1}
                    required
                  />

                  <Select
                    name="reason"
                    label="Reason"
                    options={reasonOptions.filter(
                      (option) => option.value !== "",
                    )}
                    value={newAdjustment.reason}
                    onChange={(e) =>
                      setNewAdjustment((prev) => ({
                        ...prev,
                        reason: e.target.value,
                      }))
                    }
                    required
                  />

                  <Textarea
                    name="notes"
                    label="Notes"
                    placeholder="Additional notes about this adjustment..."
                    value={newAdjustment.notes}
                    onChange={(e) =>
                      setNewAdjustment((prev) => ({
                        ...prev,
                        notes: e.target.value,
                      }))
                    }
                    rows={3}
                  />
                </FormGrid>

                <FormActions align="right">
                  <div className="flex space-x-3">
                    <CancelButton onClick={() => setShowNewAdjustment(false)}>
                      Cancel
                    </CancelButton>
                    <SubmitButton>Record Adjustment</SubmitButton>
                  </div>
                </FormActions>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
