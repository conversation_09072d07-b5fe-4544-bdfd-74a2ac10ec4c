"use client";

import {
    <PERSON><PERSON>,
    FormCard,
    FormGrid,
    Input,
    Select
} from "@/lib/forms";
import Link from "next/link";
import { useEffect, useState } from "react";

// Count-up animation hook
function useCountUp(end: number, duration: number = 2000) {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration]);

  return count;
}

// Mock report data
const reportData = {
  totalProducts: 1247,
  totalValue: 45892.50,
  lowStockItems: 23,
  outOfStockItems: 5,
  topCategories: [
    { name: "Fruits & Vegetables", value: 15420.30, percentage: 33.6 },
    { name: "Dairy & Eggs", value: 12350.80, percentage: 26.9 },
    { name: "Bakery", value: 8940.20, percentage: 19.5 },
    { name: "Beverages", value: 6180.40, percentage: 13.5 },
    { name: "Meat & Poultry", value: 3000.80, percentage: 6.5 },
  ],
  stockMovements: [
    { date: "2024-01-15", inbound: 450, outbound: 380, net: 70 },
    { date: "2024-01-14", inbound: 320, outbound: 420, net: -100 },
    { date: "2024-01-13", inbound: 280, outbound: 350, net: -70 },
    { date: "2024-01-12", inbound: 520, outbound: 290, net: 230 },
    { date: "2024-01-11", inbound: 380, outbound: 410, net: -30 },
  ],
};

export default function InventoryReportsPage() {
  const [reportType, setReportType] = useState("overview");
  const [dateRange, setDateRange] = useState({
    from: "",
    to: "",
  });

  // Count-up animations for stats
  const totalProducts = useCountUp(reportData.totalProducts);
  const totalValue = useCountUp(reportData.totalValue);
  const lowStockItems = useCountUp(reportData.lowStockItems);
  const outOfStockItems = useCountUp(reportData.outOfStockItems);

  const reportTypeOptions = [
    { value: "overview", label: "Inventory Overview" },
    { value: "valuation", label: "Inventory Valuation" },
    { value: "movement", label: "Stock Movement" },
    { value: "low-stock", label: "Low Stock Report" },
    { value: "category", label: "Category Analysis" },
    { value: "aging", label: "Inventory Aging" },
  ];

  const handleGenerateReport = () => {
    console.log("Generating report:", { reportType, dateRange });
    // Implement report generation logic
  };

  const handleExportReport = (format: string) => {
    console.log("Exporting report as:", format);
    // Implement export logic
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/inventory"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Inventory
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="flex-shrink-0 h-5 w-5 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      Reports
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Inventory Reports
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Generate comprehensive inventory reports and analytics
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="secondary" onClick={() => handleExportReport("pdf")}>
              Export PDF
            </Button>
            <Button variant="secondary" onClick={() => handleExportReport("excel")}>
              Export Excel
            </Button>
            <Button variant="primary" onClick={handleGenerateReport}>
              Generate Report
            </Button>
          </div>
        </div>
      </div>

      {/* Report Configuration */}
      <FormCard title="Report Configuration" description="Configure your inventory report parameters">
        <FormGrid cols={3} gap="md">
          <Select
            name="reportType"
            label="Report Type"
            options={reportTypeOptions}
            value={reportType}
            onChange={(e) => setReportType(e.target.value)}
          />

          <Input
            name="dateFrom"
            label="From Date"
            type="date"
            value={dateRange.from}
            onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
          />

          <Input
            name="dateTo"
            label="To Date"
            type="date"
            value={dateRange.to}
            onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
          />
        </FormGrid>
      </FormCard>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                    Total Products
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {totalProducts.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                    Total Value
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${totalValue.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                    Low Stock Items
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {lowStockItems}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                    Out of Stock
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {outOfStockItems}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Analysis */}
      <FormCard title="Top Categories by Value" description="Inventory value breakdown by category">
        <div className="space-y-4">
          {reportData.topCategories.map((category, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {category.name}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    ${category.value.toLocaleString()} ({category.percentage}%)
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${category.percentage}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </FormCard>

      {/* Stock Movement Trends */}
      <FormCard title="Stock Movement Trends" description="Recent stock inbound and outbound movements">
        <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Inbound
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Outbound
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Net Movement
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
              {reportData.stockMovements.map((movement, index) => (
                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {movement.date}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">
                    +{movement.inbound}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 dark:text-red-400">
                    -{movement.outbound}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                    movement.net >= 0
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {movement.net >= 0 ? '+' : ''}{movement.net}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </FormCard>
    </div>
  );
}
