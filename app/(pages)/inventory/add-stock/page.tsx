"use client";


import {
  Button,
  CancelButton,
  CurrencyInput,
  FormActions,
  FormCard,
  FormGrid,
  Input,
  NumberInput,
  SearchInput,
  Select,
  SubmitButton,
  Textarea
} from "@/lib/forms";
import Link from "next/link";
import { useState } from "react";

// Mock products for search
const products = [
  { id: 1, name: "Organic Bananas", sku: "ORG-BAN-001", currentStock: 150, minStock: 50, maxStock: 300 },
  { id: 2, name: "Whole Milk", sku: "MILK-WHL-001", currentStock: 25, minStock: 30, maxStock: 100 },
  { id: 3, name: "Ground Coffee", sku: "COF-GRD-001", currentStock: 0, minStock: 20, maxStock: 80 },
  { id: 4, name: "Bread - Whole Wheat", sku: "BRD-WHT-001", currentStock: 8, minStock: 15, maxStock: 50 },
];

interface StockItem {
  id: number;
  name: string;
  sku: string;
  currentStock: number;
  quantityToAdd: number;
  costPrice: number;
  expiryDate: string;
  batchNumber: string;
  supplier: string;
  location: string;
}

export default function AddStockPage() {
  const [stockItems, setStockItems] = useState<StockItem[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [stockDetails, setStockDetails] = useState({
    quantityToAdd: "",
    costPrice: "",
    expiryDate: "",
    batchNumber: "",
    supplier: "",
    location: "",
    notes: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const supplierOptions = [
    { value: "", label: "Select Supplier" },
    { value: "local-organic-farms", label: "Local Organic Farms" },
    { value: "fresh-dairy-co", label: "Fresh Dairy Co" },
    { value: "premium-coffee-inc", label: "Premium Coffee Inc" },
    { value: "wholesale-foods", label: "Wholesale Foods Ltd" },
    { value: "metro-distributors", label: "Metro Distributors" },
  ];

  const locationOptions = [
    { value: "", label: "Select Location" },
    { value: "a1-b2", label: "Aisle A1-B2" },
    { value: "a2-c1", label: "Aisle A2-C1" },
    { value: "b1-d2", label: "Aisle B1-D2" },
    { value: "b3-a1", label: "Aisle B3-A1" },
    { value: "c2-d1", label: "Aisle C2-D1" },
    { value: "warehouse", label: "Warehouse" },
    { value: "cold-storage", label: "Cold Storage" },
  ];

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelectProduct = (product: any) => {
    setSelectedProduct(product);
    setSearchQuery(product.name);
  };

  const handleAddToStock = () => {
    if (!selectedProduct || !stockDetails.quantityToAdd || !stockDetails.costPrice) {
      alert("Please fill in all required fields");
      return;
    }

    const newStockItem: StockItem = {
      id: selectedProduct.id,
      name: selectedProduct.name,
      sku: selectedProduct.sku,
      currentStock: selectedProduct.currentStock,
      quantityToAdd: parseInt(stockDetails.quantityToAdd),
      costPrice: parseFloat(stockDetails.costPrice),
      expiryDate: stockDetails.expiryDate,
      batchNumber: stockDetails.batchNumber,
      supplier: stockDetails.supplier,
      location: stockDetails.location,
    };

    setStockItems([...stockItems, newStockItem]);

    // Reset form
    setSelectedProduct(null);
    setSearchQuery("");
    setStockDetails({
      quantityToAdd: "",
      costPrice: "",
      expiryDate: "",
      batchNumber: "",
      supplier: "",
      location: "",
      notes: "",
    });
  };

  const handleRemoveItem = (index: number) => {
    setStockItems(stockItems.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (stockItems.length === 0) {
      alert("Please add at least one item to stock");
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      console.log("Stock added:", stockItems);

      // Reset form
      setStockItems([]);
      setStockDetails({
        quantityToAdd: "",
        costPrice: "",
        expiryDate: "",
        batchNumber: "",
        supplier: "",
        location: "",
        notes: "",
      });

      alert("Stock added successfully!");

    } catch (error) {
      console.error("Error adding stock:", error);
      alert("Error adding stock. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalItems = stockItems.reduce((sum, item) => sum + item.quantityToAdd, 0);
  const totalValue = stockItems.reduce((sum, item) => sum + (item.quantityToAdd * item.costPrice), 0);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/inventory"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Inventory
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="flex-shrink-0 h-5 w-5 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      Add Stock
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Add Stock
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Add new inventory to your stock levels
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Product Selection & Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Search */}
            <FormCard title="Select Product" description="Search and select a product to add stock">
              <div className="space-y-4">
                <SearchInput
                  name="productSearch"
                  label="Search Products"
                  placeholder="Search by name or SKU..."
                  value={searchQuery}
                  onSearch={handleSearch}
                  debounceMs={300}
                  showClearButton
                />

                {searchQuery && !selectedProduct && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">Search Results:</h4>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {filteredProducts.map((product) => (
                        <div
                          key={product.id}
                          className="flex items-center justify-between p-3 border border-gray-200 rounded-md cursor-pointer hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700"
                          onClick={() => handleSelectProduct(product)}
                        >
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {product.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              SKU: {product.sku} | Current Stock: {product.currentStock}
                            </div>
                          </div>
                          <Button type="button" variant="primary" size="sm">
                            Select
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedProduct && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-md dark:bg-blue-900 dark:border-blue-700">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                          Selected: {selectedProduct.name}
                        </h4>
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                          SKU: {selectedProduct.sku} | Current Stock: {selectedProduct.currentStock}
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="secondary"
                        size="sm"
                        onClick={() => {
                          setSelectedProduct(null);
                          setSearchQuery("");
                        }}
                      >
                        Change
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </FormCard>

            {/* Stock Details */}
            {selectedProduct && (
              <FormCard title="Stock Details" description="Enter details for the new stock">
                <FormGrid cols={2} gap="md">
                  <NumberInput
                    name="quantityToAdd"
                    label="Quantity to Add"
                    placeholder="Enter quantity"
                    value={stockDetails.quantityToAdd}
                    onChange={(e) => setStockDetails(prev => ({ ...prev, quantityToAdd: e.target.value }))}
                    min={1}
                    required
                  />

                  <CurrencyInput
                    name="costPrice"
                    label="Cost Price per Unit"
                    placeholder="0.00"
                    value={parseFloat(stockDetails.costPrice) || 0}
                    onChange={(value) => setStockDetails(prev => ({ ...prev, costPrice: value?.toString() ?? "" }))}
                    required
                  />

                  <Input
                    name="expiryDate"
                    label="Expiry Date"
                    type="date"
                    value={stockDetails.expiryDate}
                    onChange={(e) => setStockDetails(prev => ({ ...prev, expiryDate: e.target.value }))}
                  />

                  <Input
                    name="batchNumber"
                    label="Batch Number"
                    placeholder="Enter batch number"
                    value={stockDetails.batchNumber}
                    onChange={(e) => setStockDetails(prev => ({ ...prev, batchNumber: e.target.value }))}
                  />

                  <Select
                    name="supplier"
                    label="Supplier"
                    options={supplierOptions}
                    value={stockDetails.supplier}
                    onChange={(e) => setStockDetails(prev => ({ ...prev, supplier: e.target.value }))}
                    required
                  />

                  <Select
                    name="location"
                    label="Storage Location"
                    options={locationOptions}
                    value={stockDetails.location}
                    onChange={(e) => setStockDetails(prev => ({ ...prev, location: e.target.value }))}
                    required
                  />
                </FormGrid>

                <Textarea
                  name="notes"
                  label="Notes"
                  placeholder="Additional notes about this stock..."
                  value={stockDetails.notes}
                  onChange={(e) => setStockDetails(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                />

                <div className="flex justify-end">
                  <Button
                    type="button"
                    variant="primary"
                    onClick={handleAddToStock}
                    disabled={!selectedProduct || !stockDetails.quantityToAdd || !stockDetails.costPrice}
                  >
                    Add to Stock List
                  </Button>
                </div>
              </FormCard>
            )}
          </div>

          {/* Stock Summary */}
          <div className="space-y-6">
            <FormCard title="Stock Summary" description="Summary of items to be added">
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Items:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {totalItems}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Value:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    ${totalValue.toFixed(2)}
                  </span>
                </div>

                <div className="border-t border-gray-200 pt-4 dark:border-gray-600">
                  <div className="flex justify-between">
                    <span className="text-lg font-medium text-gray-900 dark:text-white">Items to Add:</span>
                    <span className="text-lg font-bold text-gray-900 dark:text-white">
                      {stockItems.length}
                    </span>
                  </div>
                </div>
              </div>
            </FormCard>

            {/* Stock Items List */}
            {stockItems.length > 0 && (
              <FormCard title="Items to Add" description="Review items before submitting">
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {stockItems.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 border border-gray-200 rounded-md dark:border-gray-600"
                    >
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {item.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          +{item.quantityToAdd} units @ ${item.costPrice}
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveItem(index)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </FormCard>
            )}
          </div>
        </div>

        <FormActions align="right">
          <div className="flex space-x-3">
            <Link href="/inventory">
              <CancelButton>Cancel</CancelButton>
            </Link>
            <SubmitButton
              loading={isSubmitting}
              disabled={stockItems.length === 0 || isSubmitting}
            >
              {isSubmitting ? "Adding Stock..." : `Add Stock (${stockItems.length} items)`}
            </SubmitButton>
          </div>
        </FormActions>
      </form>
    </div>
  );
}
