"use client";

import AnimatedCounter from "@/components/AnimatedCounter";
import {
  <PERSON>ton,
  FormA<PERSON>,
  FormCard,
  FormGrid,
  Input,
  Select
} from "@/lib/forms";
import { formatAmount } from "@/lib/utils";
import { useEffect, useState } from "react";

interface DashboardStats {
  sales: {
    today: { total: number; count: number; growth: number };
    week: { total: number; count: number; growth: number };
    month: { total: number; count: number; growth: number };
    year: { total: number; count: number; growth: number };
  };
  inventory: {
    totalProducts: number;
    lowStockCount: number;
    lowStockPercentage: number;
  };
  customers: {
    total: number;
  };
}

interface TopProduct {
  id: number;
  name: string;
  category: string;
  sku: string;
  units: number;
  revenue: number;
  growth: number;
  currentPrice: number;
}

interface InventoryItem {
  id: number;
  name: string;
  category: string;
  currentStock: number;
  minStock: number;
  status: "Good" | "Low" | "Out";
  lastRestocked: string | null;
  lastRestockedBy: string | null;
}

interface Customer {
  id: number;
  name: string;
  email: string | null;
  phone: string | null;
  totalSpent: number;
  totalVisits: number;
  averageOrderValue: number;
  lastVisit: string | null;
}

export default function ReportsPage() {
  const [reportFilters, setReportFilters] = useState({
    reportType: "sales",
    dateRange: "last30days",
    format: "pdf",
    category: "all",
    paymentMethod: "all",
    currency: "GHS",
    customStartDate: "",
    customEndDate: "",
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [inventoryData, setInventoryData] = useState<InventoryItem[]>([]);
  const [customerData, setCustomerData] = useState<Customer[]>([]);

  // Fetch dashboard data on component mount
  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);

      // Fetch all data in parallel
      const [dashboardResponse, topProductsResponse, inventoryResponse, customersResponse] = await Promise.all([
        fetch("/api/reports/dashboard"),
        fetch("/api/reports/top-products?limit=5"),
        fetch("/api/reports/inventory-status?limit=5"),
        fetch("/api/reports/top-customers?limit=5"),
      ]);

      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json();
        setDashboardStats(dashboardData);
      }

      if (topProductsResponse.ok) {
        const topProductsData = await topProductsResponse.json();
        setTopProducts(topProductsData.products || []);
      }

      if (inventoryResponse.ok) {
        const inventoryResponseData = await inventoryResponse.json();
        setInventoryData(inventoryResponseData.products || []);
      }

      if (customersResponse.ok) {
        const customersResponseData = await customersResponse.json();
        setCustomerData(customersResponseData.customers || []);
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const reportTypeOptions = [
    { value: "sales", label: "Sales Report" },
    { value: "inventory", label: "Inventory Report" },
    { value: "products", label: "Product Performance" },
    { value: "customers", label: "Customer Analysis" },
    { value: "financial", label: "Financial Summary" },
    { value: "tax", label: "Tax Report" },
    { value: "profit", label: "Profit & Loss" },
    { value: "cashflow", label: "Cash Flow" },
    { value: "expenses", label: "Expenses Report" },
    { value: "staff", label: "Staff Performance" },
  ];

  const dateRangeOptions = [
    { value: "today", label: "Today" },
    { value: "yesterday", label: "Yesterday" },
    { value: "last7days", label: "Last 7 Days" },
    { value: "last30days", label: "Last 30 Days" },
    { value: "thismonth", label: "This Month" },
    { value: "lastmonth", label: "Last Month" },
    { value: "thisquarter", label: "This Quarter" },
    { value: "thisyear", label: "This Year" },
    { value: "custom", label: "Custom Range" },
  ];

  const formatOptions = [
    { value: "pdf", label: "PDF" },
    { value: "excel", label: "Excel (XLSX)" },
    { value: "csv", label: "CSV" },
    { value: "print", label: "Print" },
  ];

  const categoryOptions = [
    { value: "all", label: "All Categories" },
    { value: "fruits", label: "Fruits & Vegetables" },
    { value: "dairy", label: "Dairy Products" },
    { value: "bakery", label: "Bakery" },
    { value: "beverages", label: "Beverages" },
    { value: "meat", label: "Meat & Poultry" },
    { value: "pantry", label: "Pantry Items" },
    { value: "frozen", label: "Frozen Foods" },
    { value: "household", label: "Household Items" },
  ];

  const paymentMethodOptions = [
    { value: "all", label: "All Payment Methods" },
    { value: "cash", label: "Cash" },
    { value: "credit", label: "Credit Card" },
    { value: "debit", label: "Debit Card" },
    { value: "mobile", label: "Mobile Payment" },
  ];

  const currencyOptions = [
    { value: "GHS", label: "GHS - Ghana Cedi" },
    { value: "USD", label: "USD - US Dollar" },
  ];

  const handleFilterChange = (field: string, value: string) => {
    setReportFilters(prev => ({ ...prev, [field]: value }));
  };

  const handleGenerateReport = async () => {
    setIsGenerating(true);
    console.log("Generating report with filters:", reportFilters);

    try {
      // Build query parameters
      const params = new URLSearchParams({
        type: reportFilters.reportType,
        dateRange: reportFilters.dateRange,
        category: reportFilters.category,
        paymentMethod: reportFilters.paymentMethod,
      });

      // Add custom date range if selected
      if (reportFilters.dateRange === "custom" && reportFilters.customStartDate && reportFilters.customEndDate) {
        params.append("startDate", reportFilters.customStartDate);
        params.append("endDate", reportFilters.customEndDate);
      }

      const response = await fetch(`/api/reports?${params.toString()}`);

      if (response.ok) {
        const reportData = await response.json();
        console.log("Report data:", reportData);

        // For now, just show success message
        // In a real app, you would handle the report data (download, display, etc.)
        alert(`Successfully generated ${reportFilters.reportType} report for ${reportFilters.dateRange} in ${reportFilters.format} format`);
      } else {
        throw new Error("Failed to generate report");
      }
    } catch (error) {
      console.error("Error generating report:", error);
      alert("Failed to generate report. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleExport = async (format: string) => {
    console.log(`Exporting in ${format} format`);

    try {
      // For quick exports, use predefined report types
      let reportType = "sales";
      let dateRange = "last30days";

      switch (format) {
        case "daily-sales":
          reportType = "sales";
          dateRange = "today";
          break;
        case "inventory-summary":
          reportType = "inventory";
          dateRange = "today";
          break;
        case "tax-report":
          reportType = "tax";
          dateRange = "thismonth";
          break;
        case "financial-summary":
          reportType = "financial";
          dateRange = "thismonth";
          break;
        default:
          // For table exports, use current filters
          reportType = reportFilters.reportType;
          dateRange = reportFilters.dateRange;
      }

      const params = new URLSearchParams({
        type: reportType,
        dateRange: dateRange,
        category: reportFilters.category,
        paymentMethod: reportFilters.paymentMethod,
      });

      const response = await fetch(`/api/reports?${params.toString()}`);

      if (response.ok) {
        const reportData = await response.json();
        console.log("Export data:", reportData);

        // In a real app, you would process the data and trigger download
        // For now, just show success message
        alert(`Successfully exported ${reportType} report in ${format} format`);
      } else {
        throw new Error("Failed to export report");
      }
    } catch (error) {
      console.error("Error exporting report:", error);
      alert("Failed to export report. Please try again.");
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Reports & Analytics
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Comprehensive business insights and detailed reporting for your supermarket
          </p>
        </div>
        <Button
          variant="secondary"
          onClick={fetchDashboardData}
          disabled={isLoading}
        >
          {isLoading ? "Refreshing..." : "Refresh Data"}
        </Button>
      </div>

      {/* Quick Stats Dashboard */}
      {isLoading ? (
        <FormGrid cols={4} gap="md">
          {[1, 2, 3, 4].map((i) => (
            <FormCard key={i} title="Loading...">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-400 dark:text-gray-600">
                  <div className="animate-pulse">---</div>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Loading...</p>
              </div>
            </FormCard>
          ))}
        </FormGrid>
      ) : (
        <FormGrid cols={4} gap="md">
          <FormCard title="Today's Sales">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                <AnimatedCounter amount={dashboardStats?.sales.today.total || 0} />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {dashboardStats?.sales.today.growth !== undefined && (
                  <span className={dashboardStats.sales.today.growth >= 0 ? "text-green-600" : "text-red-600"}>
                    {dashboardStats.sales.today.growth >= 0 ? "+" : ""}{dashboardStats.sales.today.growth.toFixed(1)}% from yesterday
                  </span>
                )}
              </p>
            </div>
          </FormCard>

          <FormCard title="This Week">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                <AnimatedCounter amount={dashboardStats?.sales.week.total || 0} />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {dashboardStats?.sales.week.growth !== undefined && (
                  <span className={dashboardStats.sales.week.growth >= 0 ? "text-green-600" : "text-red-600"}>
                    {dashboardStats.sales.week.growth >= 0 ? "+" : ""}{dashboardStats.sales.week.growth.toFixed(1)}% from last week
                  </span>
                )}
              </p>
            </div>
          </FormCard>

          <FormCard title="This Month">
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                <AnimatedCounter amount={dashboardStats?.sales.month.total || 0} />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {dashboardStats?.sales.month.growth !== undefined && (
                  <span className={dashboardStats.sales.month.growth >= 0 ? "text-green-600" : "text-red-600"}>
                    {dashboardStats.sales.month.growth >= 0 ? "+" : ""}{dashboardStats.sales.month.growth.toFixed(1)}% from last month
                  </span>
                )}
              </p>
            </div>
          </FormCard>

          <FormCard title="This Year">
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
                <AnimatedCounter amount={dashboardStats?.sales.year.total || 0} />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {dashboardStats?.sales.year.growth !== undefined && (
                  <span className={dashboardStats.sales.year.growth >= 0 ? "text-green-600" : "text-red-600"}>
                    {dashboardStats.sales.year.growth >= 0 ? "+" : ""}{dashboardStats.sales.year.growth.toFixed(1)}% from last year
                  </span>
                )}
              </p>
            </div>
          </FormCard>
        </FormGrid>
      )}

      {/* Report Generation Form */}
      <FormCard title="Generate Custom Report" description="Create detailed reports with custom filters and export options">
        <FormGrid cols={3} gap="md">
          <Select
            name="reportType"
            label="Report Type"
            value={reportFilters.reportType}
            onChange={(e) => handleFilterChange("reportType", e.target.value)}
            options={reportTypeOptions}
            required
          />

          <Select
            name="dateRange"
            label="Date Range"
            value={reportFilters.dateRange}
            onChange={(e) => handleFilterChange("dateRange", e.target.value)}
            options={dateRangeOptions}
            required
          />

          <Select
            name="format"
            label="Export Format"
            value={reportFilters.format}
            onChange={(e) => handleFilterChange("format", e.target.value)}
            options={formatOptions}
            required
          />

          <Select
            name="category"
            label="Product Category"
            value={reportFilters.category}
            onChange={(e) => handleFilterChange("category", e.target.value)}
            options={categoryOptions}
          />

          <Select
            name="paymentMethod"
            label="Payment Method"
            value={reportFilters.paymentMethod}
            onChange={(e) => handleFilterChange("paymentMethod", e.target.value)}
            options={paymentMethodOptions}
          />

          <Select
            name="currency"
            label="Currency"
            value={reportFilters.currency}
            onChange={(e) => handleFilterChange("currency", e.target.value)}
            options={currencyOptions}
          />
        </FormGrid>

        {reportFilters.dateRange === "custom" && (
          <FormGrid cols={2} gap="md" className="mt-4">
            <Input
              name="customStartDate"
              label="Start Date"
              type="date"
              value={reportFilters.customStartDate}
              onChange={(e) => handleFilterChange("customStartDate", e.target.value)}
            />
            <Input
              name="customEndDate"
              label="End Date"
              type="date"
              value={reportFilters.customEndDate}
              onChange={(e) => handleFilterChange("customEndDate", e.target.value)}
            />
          </FormGrid>
        )}

        <FormActions align="right" className="mt-6">
          <Button
            variant="primary"
            onClick={handleGenerateReport}
            loading={isGenerating}
            disabled={isGenerating}
          >
            {isGenerating ? "Generating..." : "Generate Report"}
          </Button>
        </FormActions>
      </FormCard>

      {/* Report Data Tables */}
      <FormGrid cols={2} gap="lg">
        {/* Top Products Report */}
        <FormCard title="Top Selling Products" description="Best performing products by revenue">
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                    Product
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                    Units
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                    Revenue
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                    Growth
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                {topProducts.map((product, index) => (
                  <tr key={index}>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {product.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {product.category}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {product.units}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {formatAmount(product.revenue)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        product.growth > 0
                          ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                          : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                      }`}>
                        {product.growth > 0 ? '+' : ''}{product.growth}%
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <FormActions align="right" className="mt-4">
            <Button variant="secondary" size="sm" onClick={() => handleExport('csv')}>
              Export CSV
            </Button>
          </FormActions>
        </FormCard>

        {/* Inventory Status Report */}
        <FormCard title="Inventory Status" description="Current stock levels and alerts">
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                    Product
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                    Stock
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                    Last Restocked
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                {inventoryData.map((item, index) => (
                  <tr key={index}>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {item.name}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {item.currentStock} / {item.minStock}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        item.status === 'Good'
                          ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                          : item.status === 'Low'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                          : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                      }`}>
                        {item.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {item.lastRestocked}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <FormActions align="right" className="mt-4">
            <Button variant="secondary" size="sm" onClick={() => handleExport('pdf')}>
              Export PDF
            </Button>
          </FormActions>
        </FormCard>
      </FormGrid>

      {/* Customer Analytics */}
      <FormCard title="Top Customers" description="Most valuable customers by total spending">
        <div className="overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Customer Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Total Spent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Visits
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Avg Order
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Last Visit
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
              {customerData.map((customer, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {customer.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">
                    {formatAmount(customer.totalSpent)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {customer.totalVisits}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatAmount(customer.averageOrderValue)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {customer.lastVisit}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <FormActions align="right" className="mt-4">
          <div className="flex space-x-2">
            <Button variant="secondary" size="sm" onClick={() => handleExport('excel')}>
              Export Excel
            </Button>
            <Button variant="secondary" size="sm" onClick={() => handleExport('pdf')}>
              Export PDF
            </Button>
          </div>
        </FormActions>
      </FormCard>

      {/* Quick Export Actions */}
      <FormCard title="Quick Export" description="Export common reports instantly">
        <FormGrid cols={4} gap="md">
          <Button variant="secondary" onClick={() => handleExport('daily-sales')}>
            Daily Sales Report
          </Button>
          <Button variant="secondary" onClick={() => handleExport('inventory-summary')}>
            Inventory Summary
          </Button>
          <Button variant="secondary" onClick={() => handleExport('tax-report')}>
            Tax Report
          </Button>
          <Button variant="secondary" onClick={() => handleExport('financial-summary')}>
            Financial Summary
          </Button>
        </FormGrid>
      </FormCard>
    </div>
  );
}
