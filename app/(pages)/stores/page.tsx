"use client";

import AddStoreModal from "@/components/AddStoreModal";
import { StoreGrid } from "@/components/StoreSelector";
import { Button, FormActions, FormCard, Pagination, SearchInput } from "@/lib/forms";
import { useServerPagination } from "@/lib/pagination";
import { useEffect, useState } from "react";

interface Store {
  id: number;
  name: string;
  address: string;
  location: string;
  phoneNumber: string;
  email: string;
  logoUrl?: string | null;
  stats?: {
    totalUsers: number;
    totalProducts: number;
    totalSales: number;
    monthlyRevenue: number;
  };
}

interface StoresResponse {
  stores: Store[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  filters: {
    search: string;
    includeStats: boolean;
  };
}

export default function StoresPage() {
  const [stores, setStores] = useState<Store[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStoreId, setSelectedStoreId] = useState<number | undefined>();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Initialize pagination
  const pagination = useServerPagination({
    totalItems,
    initialPageSize: 8, // Good for grid layout
    useUrlParams: true,
  });

  useEffect(() => {
    fetchStores();
  }, [pagination.currentPage, pagination.pageSize, searchQuery]);

  const fetchStores = async () => {
    try {
      setIsLoading(true);

      // Build query parameters
      const params = new URLSearchParams({
        includeStats: "true",
        page: pagination.currentPage.toString(),
        limit: pagination.pageSize.toString(),
      });

      if (searchQuery) {
        params.set("search", searchQuery);
      }

      const response = await fetch(`/api/stores?${params.toString()}`);

      if (response.ok) {
        const data: StoresResponse = await response.json();
        setStores(data.stores || []);
        setTotalItems(data.pagination.totalCount);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStoreSelect = (storeId: number) => {
    setSelectedStoreId(storeId);
  };

  const handleStoreAdded = () => {
    fetchStores(); // Refresh the stores list
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Reset to first page when searching
    pagination.setPage(1);
  };

  const totalRevenue = stores.reduce((sum, store) => sum + (store.stats?.monthlyRevenue || 0), 0);
  const totalSales = stores.reduce((sum, store) => sum + (store.stats?.totalSales || 0), 0);
  const totalProducts = stores.reduce((sum, store) => sum + (store.stats?.totalProducts || 0), 0);
  const totalStaff = stores.reduce((sum, store) => sum + (store.stats?.totalUsers || 0), 0);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Store Management
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage your store locations and view performance metrics
          </p>
        </div>
        <Button
          variant="primary"
          onClick={() => setIsAddModalOpen(true)}
        >
          Add New Store
        </Button>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                    Total Stores
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {stores.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                    Total Revenue (30d)
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ₵{totalRevenue.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                    Total Products
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {totalProducts.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                    Total Staff
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {totalStaff}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <FormCard title="Search Stores">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <SearchInput
              placeholder="Search stores by name, location, or address..."
              onSearch={handleSearch}
              value={searchQuery}
            />
          </div>
          <Button
            variant="secondary"
            onClick={fetchStores}
            disabled={isLoading}
          >
            {isLoading ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </FormCard>

      {/* Stores Grid */}
      <FormCard title={`Stores (${totalItems})`}>
        <StoreGrid
          stores={stores}
          selectedStoreId={selectedStoreId}
          onStoreSelect={handleStoreSelect}
          isLoading={isLoading}
        />

        {!isLoading && stores.length === 0 && searchQuery && (
          <div className="text-center py-8">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No stores found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              No stores match your search criteria. Try adjusting your search terms.
            </p>
          </div>
        )}

        {/* Pagination */}
        {!isLoading && totalItems > 0 && (
          <div className="mt-6">
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={pagination.setPage}
              showInfo={true}
              showPageSize={true}
              pageSize={pagination.pageSize}
              onPageSizeChange={pagination.setPageSize}
              totalItems={totalItems}
            />
          </div>
        )}
      </FormCard>

      {/* Selected Store Details */}
      {selectedStoreId && (
        <FormCard title="Store Actions">
          <FormActions align="left">
            <Button variant="primary">
              View Details
            </Button>
            <Button variant="secondary">
              Edit Store
            </Button>
            <Button variant="secondary">
              Manage Staff
            </Button>
            <Button variant="secondary">
              View Reports
            </Button>
          </FormActions>
        </FormCard>
      )}

      {/* Add Store Modal */}
      <AddStoreModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onStoreAdded={handleStoreAdded}
      />
    </div>
  );
}
