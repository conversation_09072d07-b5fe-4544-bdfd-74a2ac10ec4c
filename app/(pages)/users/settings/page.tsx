"use client";

import {
  Checkbox,
  FormActions,
  FormCard,
  FormGrid,
  Input,
  NumberInput,
  SaveButton,
  Select,
  Textarea,
} from "@/lib/forms";
import { useState } from "react";

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    businessName: "Shopper Supermarket",
    address: "123 Main Street, Accra, Ghana",
    phone: "+*********** 789",
    email: "<EMAIL>",
    taxRate: 8.25,
    taxNumber: "12-3456789",
    includeTax: true,
    receiptHeader: "Thank you for shopping with us!",
    receiptFooter: "Visit us again soon!",
    printReceipt: true,
    cash: true,
    creditCard: true,
    debitCard: true,
    mobilePayment: false,
    currency: "GHS",
    timezone: "Africa/Accra",
    language: "en",
  });

  const handleInputChange = (
    field: string,
    value: string | number | boolean,
  ) => {
    setSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    console.log("Saving settings:", settings);
    // TODO: Implement save functionality
  };

  const currencyOptions = [
    { value: "GHS", label: "GHS - Ghana Cedi" },
    { value: "USD", label: "USD - US Dollar" },
  ];

  const timezoneOptions = [
    { value: "Africa/Accra", label: "Accra, Ghana (GMT+0)" },
    { value: "America/Lisbon", label: "Lisbon, Portugal (GMT+0)" },
  ];

  const languageOptions = [{ value: "en", label: "English" }];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Settings
        </h1>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
          Configure your POS system and business settings
        </p>
      </div>

      {/* Settings Sections */}
      <FormGrid cols={2} gap="lg">
        {/* Business Information */}
        <FormCard title="Business Information">
          <div className="space-y-4">
            <Input
              name="businessName"
              label="Business Name"
              value={settings.businessName}
              onChange={(e) =>
                handleInputChange("businessName", e.target.value)
              }
              required
            />

            <Textarea
              name="address"
              label="Address"
              value={settings.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              rows={3}
            />

            <FormGrid cols={2} gap="md">
              <Input
                name="phone"
                label="Phone"
                type="tel"
                value={settings.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
              />

              <Input
                name="email"
                label="Email"
                type="email"
                value={settings.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
              />
            </FormGrid>
          </div>
        </FormCard>

        {/* Tax Settings */}
        <FormCard title="Tax Settings">
          <div className="space-y-4">
            <NumberInput
              name="taxRate"
              label="Default Tax Rate (%)"
              value={settings.taxRate.toString()}
              onChange={(e) =>
                handleInputChange("taxRate", parseFloat(e.target.value) || 0)
              }
              step={0.01}
              min={0}
              max={100}
            />

            <Input
              name="taxNumber"
              label="Tax ID Number"
              value={settings.taxNumber}
              onChange={(e) => handleInputChange("taxNumber", e.target.value)}
            />

            <Checkbox
              name="includeTax"
              label="Include tax in product prices"
              checked={settings.includeTax}
              onChange={(e) =>
                handleInputChange("includeTax", e.target.checked)
              }
            />
          </div>
        </FormCard>

        {/* Receipt Settings */}
        <FormCard title="Receipt Settings">
          <div className="space-y-4">
            <Textarea
              name="receiptHeader"
              label="Receipt Header"
              value={settings.receiptHeader}
              onChange={(e) =>
                handleInputChange("receiptHeader", e.target.value)
              }
              rows={2}
            />

            <Textarea
              name="receiptFooter"
              label="Receipt Footer"
              value={settings.receiptFooter}
              onChange={(e) =>
                handleInputChange("receiptFooter", e.target.value)
              }
              rows={2}
            />

            <Checkbox
              name="printReceipt"
              label="Auto-print receipts"
              checked={settings.printReceipt}
              onChange={(e) =>
                handleInputChange("printReceipt", e.target.checked)
              }
            />
          </div>
        </FormCard>

        {/* Payment Methods */}
        <FormCard title="Payment Methods">
          <div className="space-y-4">
            <Checkbox
              name="cash"
              label="Cash"
              checked={settings.cash}
              onChange={(e) => handleInputChange("cash", e.target.checked)}
            />

            <Checkbox
              name="creditCard"
              label="Credit Card"
              checked={settings.creditCard}
              onChange={(e) =>
                handleInputChange("creditCard", e.target.checked)
              }
            />

            <Checkbox
              name="debitCard"
              label="Debit Card"
              checked={settings.debitCard}
              onChange={(e) => handleInputChange("debitCard", e.target.checked)}
            />

            <Checkbox
              name="mobilePayment"
              label="Mobile Payment (Apple Pay, Google Pay)"
              checked={settings.mobilePayment}
              onChange={(e) =>
                handleInputChange("mobilePayment", e.target.checked)
              }
            />
          </div>
        </FormCard>
      </FormGrid>

      {/* System Settings */}
      <FormCard title="System Settings">
        <FormGrid cols={3} gap="md">
          <Select
            name="currency"
            label="Currency"
            value={settings.currency}
            onChange={(e) => handleInputChange("currency", e.target.value)}
            options={currencyOptions}
          />

          <Select
            name="timezone"
            label="Timezone"
            value={settings.timezone}
            onChange={(e) => handleInputChange("timezone", e.target.value)}
            options={timezoneOptions}
          />

          <Select
            name="language"
            label="Language"
            value={settings.language}
            onChange={(e) => handleInputChange("language", e.target.value)}
            options={languageOptions}
          />
        </FormGrid>
      </FormCard>

      {/* Save Button */}
      <FormActions align="right">
        <SaveButton onClick={handleSave}>Save Settings</SaveButton>
      </FormActions>
    </div>
  );
}
