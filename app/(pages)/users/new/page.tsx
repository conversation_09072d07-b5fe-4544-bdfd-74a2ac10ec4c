"use client";

import { useSelectedStore } from "@/hooks/useStores";
import {
  CancelButton,
  FormActions,
  FormCard,
  FormGrid,
  Input,
  Select,
  SubmitButton,
} from "@/lib/forms";
import Link from "next/link";
import { useEffect, useState } from "react";

interface UserFormData {
  name: string;
  email: string;
  password: string;
  role: "ADMIN" | "MANAGER" | "CASHIER" | "STAFF";
  gender?: "MALE" | "FEMALE" | "OTHER";
  phone?: string;
  address?: string;
  homeTown?: string;
  staffId?: string;
  contactPerson?: string;
  contactPersonPhone?: string;
  active: boolean;
  remarks?: string;
  comment?: string;
  storeId: number;
}

export default function NewUserPage() {
  const { selectedStoreId, stores } = useSelectedStore();
  const [formData, setFormData] = useState<UserFormData>({
    name: "",
    email: "",
    password: "",
    role: "CASHIER",
    gender: undefined,
    phone: "",
    address: "",
    homeTown: "",
    staffId: "",
    contactPerson: "",
    contactPersonPhone: "",
    active: true,
    remarks: "",
    comment: "",
    storeId: selectedStoreId || 0,
  });

  // Update storeId when selectedStoreId changes
  useEffect(() => {
    if (selectedStoreId) {
      setFormData(prev => ({ ...prev, storeId: selectedStoreId }));
    }
  }, [selectedStoreId]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const roleOptions = [
    { value: "CASHIER", label: "Cashier" },
    { value: "STAFF", label: "Staff" },
    { value: "MANAGER", label: "Manager" },
    { value: "ADMIN", label: "Admin" },
  ];

  const genderOptions = [
    { value: "", label: "Select Gender (Optional)" },
    { value: "MALE", label: "Male" },
    { value: "FEMALE", label: "Female" },
    { value: "OTHER", label: "Other" },
  ];

  const storeOptions = stores.map(store => ({
    value: store.id.toString(),
    label: `${store.name} - ${store.location}`,
  }));

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    // Basic validation
    const newErrors: Record<string, string> = {};
    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!/\S+@\S+\.\S+/.test(formData.email))
      newErrors.email = "Email is invalid";
    if (!formData.password.trim()) newErrors.password = "Password is required";
    if (formData.password.length < 6) newErrors.password = "Password must be at least 6 characters";
    if (!formData.storeId) newErrors.storeId = "Store is required";

    // Optional field validations
    if (formData.phone && !/^[\+]?[0-9\s\-\(\)]{10,}$/.test(formData.phone)) {
      newErrors.phone = "Please enter a valid phone number";
    }
    if (formData.contactPersonPhone && !/^[\+]?[0-9\s\-\(\)]{10,}$/.test(formData.contactPersonPhone)) {
      newErrors.contactPersonPhone = "Please enter a valid phone number";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Prepare the data for submission
      const submitData = {
        ...formData,
        // Convert empty strings to null for optional fields
        phone: formData.phone?.trim() || null,
        address: formData.address?.trim() || null,
        homeTown: formData.homeTown?.trim() || null,
        staffId: formData.staffId?.trim() || null,
        contactPerson: formData.contactPerson?.trim() || null,
        contactPersonPhone: formData.contactPersonPhone?.trim() || null,
        remarks: formData.remarks?.trim() || null,
        comment: formData.comment?.trim() || null,
        gender: formData.gender || null,
      };

      const response = await fetch("/api/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.fieldErrors) {
          setErrors(errorData.fieldErrors);
        } else {
          setErrors({ general: errorData.error || "Failed to create user" });
        }
        return;
      }

      // Success - redirect to users list
      window.location.href = "/users";
    } catch {
      setErrors({ general: "Failed to create user" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof UserFormData, value: string | number | boolean | undefined) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/users"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Users
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="h-5 w-5 flex-shrink-0 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      New User
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Add New User
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Create a new user account for the system
            </p>
          </div>
        </div>
      </div>

      {/* User Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {errors.general && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors.general}
            </p>
          </div>
        )}

        <FormCard
          title="Basic Information"
          description="Essential user account details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="name"
              label="Full Name"
              placeholder="Enter full name"
              required
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              error={errors.name}
            />

            <Input
              name="email"
              label="Email Address"
              type="email"
              placeholder="Enter email address"
              required
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              error={errors.email}
            />

            <Input
              name="password"
              label="Password"
              type="password"
              placeholder="Enter password"
              required
              value={formData.password}
              onChange={(e) => handleInputChange("password", e.target.value)}
              error={errors.password}
            />

            <Select
              name="role"
              label="Role"
              value={formData.role}
              onChange={(e) => handleInputChange("role", e.target.value as UserFormData["role"])}
              options={roleOptions}
              required
              error={errors.role}
            />

            <Select
              name="gender"
              label="Gender"
              value={formData.gender || ""}
              onChange={(e) => handleInputChange("gender", e.target.value || undefined)}
              options={genderOptions}
              error={errors.gender}
            />

            <Select
              name="storeId"
              label="Store"
              value={formData.storeId.toString()}
              onChange={(e) => handleInputChange("storeId", parseInt(e.target.value))}
              options={storeOptions}
              required
              error={errors.storeId}
            />
          </FormGrid>
        </FormCard>

        <FormCard
          title="Contact Information"
          description="Phone number and address details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="phone"
              label="Phone Number"
              type="tel"
              placeholder="Enter phone number"
              value={formData.phone || ""}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              error={errors.phone}
            />

            <Input
              name="homeTown"
              label="Home Town"
              placeholder="Enter home town"
              value={formData.homeTown || ""}
              onChange={(e) => handleInputChange("homeTown", e.target.value)}
              error={errors.homeTown}
            />
          </FormGrid>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Address
            </label>
            <textarea
              name="address"
              placeholder="Enter full address"
              rows={3}
              value={formData.address || ""}
              onChange={(e) => handleInputChange("address", e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            {errors.address && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.address}</p>
            )}
          </div>
        </FormCard>

        <FormCard
          title="Staff Information"
          description="Staff ID and employment details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="staffId"
              label="Staff ID"
              placeholder="Enter staff ID (optional)"
              value={formData.staffId || ""}
              onChange={(e) => handleInputChange("staffId", e.target.value)}
              error={errors.staffId}
            />

            <div className="flex items-center">
              <input
                type="checkbox"
                id="active"
                name="active"
                checked={formData.active}
                onChange={(e) => handleInputChange("active", e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="active" className="ml-2 block text-sm text-gray-900 dark:text-white">
                Active Employee
              </label>
            </div>
          </FormGrid>
        </FormCard>

        <FormCard
          title="Emergency Contact"
          description="Emergency contact person details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="contactPerson"
              label="Contact Person Name"
              placeholder="Enter emergency contact name"
              value={formData.contactPerson || ""}
              onChange={(e) => handleInputChange("contactPerson", e.target.value)}
              error={errors.contactPerson}
            />

            <Input
              name="contactPersonPhone"
              label="Contact Person Phone"
              type="tel"
              placeholder="Enter emergency contact phone"
              value={formData.contactPersonPhone || ""}
              onChange={(e) => handleInputChange("contactPersonPhone", e.target.value)}
              error={errors.contactPersonPhone}
            />
          </FormGrid>
        </FormCard>

        <FormCard
          title="Additional Information"
          description="Remarks and comments"
        >
          <FormGrid cols={1} gap="md">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Remarks
              </label>
              <textarea
                name="remarks"
                placeholder="Enter any remarks about the employee"
                rows={3}
                value={formData.remarks || ""}
                onChange={(e) => handleInputChange("remarks", e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              {errors.remarks && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.remarks}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Comments
              </label>
              <textarea
                name="comment"
                placeholder="Enter any additional comments"
                rows={3}
                value={formData.comment || ""}
                onChange={(e) => handleInputChange("comment", e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              {errors.comment && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.comment}</p>
              )}
            </div>
          </FormGrid>
        </FormCard>

        <FormActions align="right">
          <div className="flex space-x-3">
            <Link href="/users">
              <CancelButton>Cancel</CancelButton>
            </Link>
            <SubmitButton loading={isSubmitting} disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create User"}
            </SubmitButton>
          </div>
        </FormActions>
      </form>
    </div>
  );
}
