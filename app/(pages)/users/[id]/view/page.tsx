"use client";

import { FormCard, FormGrid } from "@/lib/forms";
import Link from "next/link";
import { useEffect, useState } from "react";

interface User {
  id: number;
  email: string;
  name: string | null;
  role: "ADMIN" | "MANAGER" | "CASHIER" | "STAFF";
  gender?: "MALE" | "FEMALE" | "OTHER" | null;
  phone?: string | null;
  address?: string | null;
  homeTown?: string | null;
  staffId?: string | null;
  contactPerson?: string | null;
  contactPersonPhone?: string | null;
  active: boolean;
  remarks?: string | null;
  comment?: string | null;
  storeId: number;
  store?: {
    id: number;
    name: string;
    location: string;
  };
  createdAt: string;
  updatedAt: string;
}

export default function UserViewPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      try {
        const resolvedParams = await params;
        const response = await fetch(`/api/users/${resolvedParams.id}`);
        if (!response.ok) {
          throw new Error("User not found");
        }
        const userData = await response.json();
        setUser(userData);
      } catch (error) {
        console.error("Error loading user:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, [params]);

  if (isLoading) {
    return (
      <div className="flex min-h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="py-12 text-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          User not found
        </h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          The user you're looking for doesn't exist.
        </p>
        <Link
          href="/users"
          className="mt-4 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-600 hover:bg-blue-200"
        >
          Back to Users
        </Link>
      </div>
    );
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "MANAGER":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "CASHIER":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "STAFF":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getStatusBadgeColor = (active: boolean) => {
    return active
      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/users"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Users
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="h-5 w-5 flex-shrink-0 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      {user.name || user.email}
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              User Profile
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              View user information and details
            </p>
          </div>
          <div className="flex space-x-3">
            <Link
              href={`/users/${user.id}`}
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <svg
                className="mr-2 h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              Edit User
            </Link>
          </div>
        </div>
      </div>

      {/* User Profile Header */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-20 w-20 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                <svg
                  className="h-12 w-12 text-gray-400 dark:text-gray-300"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div className="ml-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                {user.name || "No Name"}
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {user.email}
              </p>
              <div className="mt-2 flex items-center space-x-3">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeColor(
                    user.role
                  )}`}
                >
                  {user.role}
                </span>
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(
                    user.active
                  )}`}
                >
                  {user.active ? "Active" : "Inactive"}
                </span>
                {user.staffId && (
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Staff ID: {user.staffId}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* User Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <FormCard
          title="Basic Information"
          description="Essential user account details"
        >
          <FormGrid cols={1} gap="md">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Full Name
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.name || "Not provided"}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email Address
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.email}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Role
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.role}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Gender
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.gender || "Not specified"}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Store Assignment
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.store ? `${user.store.name} - ${user.store.location}` : "Not assigned"}
              </p>
            </div>
          </FormGrid>
        </FormCard>

        {/* Contact Information */}
        <FormCard
          title="Contact Information"
          description="Phone number and address details"
        >
          <FormGrid cols={1} gap="md">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Phone Number
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.phone || "Not provided"}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Home Town
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.homeTown || "Not provided"}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Address
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                {user.address || "Not provided"}
              </p>
            </div>
          </FormGrid>
        </FormCard>

        {/* Staff Information */}
        <FormCard
          title="Staff Information"
          description="Employment and staff details"
        >
          <FormGrid cols={1} gap="md">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Staff ID
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.staffId || "Not assigned"}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Employment Status
              </label>
              <p className="mt-1">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(
                    user.active
                  )}`}
                >
                  {user.active ? "Active Employee" : "Inactive Employee"}
                </span>
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Account Created
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {new Date(user.createdAt).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Last Updated
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {new Date(user.updatedAt).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </p>
            </div>
          </FormGrid>
        </FormCard>

        {/* Emergency Contact */}
        <FormCard
          title="Emergency Contact"
          description="Emergency contact person details"
        >
          <FormGrid cols={1} gap="md">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Contact Person Name
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.contactPerson || "Not provided"}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Contact Person Phone
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.contactPersonPhone || "Not provided"}
              </p>
            </div>
          </FormGrid>
        </FormCard>
      </div>

      {/* Additional Information */}
      {(user.remarks || user.comment) && (
        <div className="grid grid-cols-1 gap-6">
          <FormCard
            title="Additional Information"
            description="Remarks and comments"
          >
            <FormGrid cols={1} gap="md">
              {user.remarks && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Remarks
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                    {user.remarks}
                  </p>
                </div>
              )}

              {user.comment && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Comments
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                    {user.comment}
                  </p>
                </div>
              )}
            </FormGrid>
          </FormCard>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Link
          href="/users"
          className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
        >
          <svg
            className="mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          Back to Users
        </Link>

        <Link
          href={`/users/${user.id}`}
          className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <svg
            className="mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
          Edit User
        </Link>
      </div>
    </div>
  );
}
