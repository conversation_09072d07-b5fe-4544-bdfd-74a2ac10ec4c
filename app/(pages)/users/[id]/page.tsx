"use client";

import { useSelectedStore } from "@/hooks/useStores";
import {
  CancelButton,
  DeleteButton,
  FormActions,
  FormCard,
  FormGrid,
  Input,
  Select,
  SubmitButton,
} from "@/lib/forms";
import Link from "next/link";
import { useEffect, useState } from "react";

interface User {
  id: number;
  email: string;
  name: string | null;
  role: "ADMIN" | "MANAGER" | "CASHIER" | "STAFF";
  gender?: "MALE" | "FEMALE" | "OTHER" | null;
  phone?: string | null;
  address?: string | null;
  homeTown?: string | null;
  staffId?: string | null;
  contactPerson?: string | null;
  contactPersonPhone?: string | null;
  active: boolean;
  remarks?: string | null;
  comment?: string | null;
  storeId: number;
  store?: {
    id: number;
    name: string;
    location: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface UserFormData {
  name: string;
  email: string;
  role: "ADMIN" | "MANAGER" | "CASHIER" | "STAFF";
  gender?: "MALE" | "FEMALE" | "OTHER";
  phone?: string;
  address?: string;
  homeTown?: string;
  staffId?: string;
  contactPerson?: string;
  contactPersonPhone?: string;
  active: boolean;
  remarks?: string;
  comment?: string;
  storeId: number;
}

export default function UserDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { stores } = useSelectedStore();
  const [user, setUser] = useState<User | null>(null);
  const [formData, setFormData] = useState<UserFormData>({
    name: "",
    email: "",
    role: "CASHIER",
    gender: undefined,
    phone: "",
    address: "",
    homeTown: "",
    staffId: "",
    contactPerson: "",
    contactPersonPhone: "",
    active: true,
    remarks: "",
    comment: "",
    storeId: 0,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const roleOptions = [
    { value: "CASHIER", label: "Cashier" },
    { value: "STAFF", label: "Staff" },
    { value: "MANAGER", label: "Manager" },
    { value: "ADMIN", label: "Admin" },
  ];

  const genderOptions = [
    { value: "", label: "Select Gender (Optional)" },
    { value: "MALE", label: "Male" },
    { value: "FEMALE", label: "Female" },
    { value: "OTHER", label: "Other" },
  ];

  const storeOptions = stores.map(store => ({
    value: store.id.toString(),
    label: `${store.name} - ${store.location}`,
  }));

  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      try {
        const resolvedParams = await params;
        const response = await fetch(`/api/users/${resolvedParams.id}`);
        if (!response.ok) {
          throw new Error("User not found");
        }
        const userData = await response.json();
        setUser(userData);
        setFormData({
          name: userData.name || "",
          email: userData.email,
          role: userData.role,
          gender: userData.gender || undefined,
          phone: userData.phone || "",
          address: userData.address || "",
          homeTown: userData.homeTown || "",
          staffId: userData.staffId || "",
          contactPerson: userData.contactPerson || "",
          contactPersonPhone: userData.contactPersonPhone || "",
          active: userData.active,
          remarks: userData.remarks || "",
          comment: userData.comment || "",
          storeId: userData.storeId,
        });
      } catch (error) {
        console.error("Error loading user:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, [params]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    // Basic validation
    const newErrors: Record<string, string> = {};
    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!/\S+@\S+\.\S+/.test(formData.email))
      newErrors.email = "Email is invalid";
    if (!formData.storeId) newErrors.storeId = "Store is required";

    // Optional field validations
    if (formData.phone && !/^[\+]?[0-9\s\-\(\)]{10,}$/.test(formData.phone)) {
      newErrors.phone = "Please enter a valid phone number";
    }
    if (formData.contactPersonPhone && !/^[\+]?[0-9\s\-\(\)]{10,}$/.test(formData.contactPersonPhone)) {
      newErrors.contactPersonPhone = "Please enter a valid phone number";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      const resolvedParams = await params;

      // Prepare the data for submission
      const submitData = {
        ...formData,
        // Convert empty strings to null for optional fields
        phone: formData.phone?.trim() || null,
        address: formData.address?.trim() || null,
        homeTown: formData.homeTown?.trim() || null,
        staffId: formData.staffId?.trim() || null,
        contactPerson: formData.contactPerson?.trim() || null,
        contactPersonPhone: formData.contactPersonPhone?.trim() || null,
        remarks: formData.remarks?.trim() || null,
        comment: formData.comment?.trim() || null,
        gender: formData.gender || null,
      };

      const response = await fetch(`/api/users/${resolvedParams.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.fieldErrors) {
          setErrors(errorData.fieldErrors);
        } else {
          setErrors({ general: errorData.error || "Failed to update user" });
        }
        return;
      }

      // Success - redirect to users list
      window.location.href = "/users";
    } catch {
      setErrors({ general: "Failed to update user" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof UserFormData, value: string | number | boolean | undefined) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleDelete = async () => {
    if (confirm("Are you sure you want to delete this user?")) {
      try {
        const resolvedParams = await params;
        const response = await fetch(`/api/users/${resolvedParams.id}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to delete user");
        }

        // Redirect to users list
        window.location.href = "/users";
      } catch (error) {
        console.error("Error deleting user:", error);
        setErrors({ general: "Failed to delete user" });
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="py-12 text-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          User not found
        </h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          The user you're looking for doesn&apos;t exist.
        </p>
        <Link
          href="/users"
          className="mt-4 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-600 hover:bg-blue-200"
        >
          Back to Users
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/users"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Users
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="h-5 w-5 flex-shrink-0 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      {user.name || user.email}
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Edit User
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Update user information and role
            </p>
          </div>
          <div className="flex space-x-3">
            <Link
              href={`/users/${user.id}/view`}
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <svg
                className="mr-2 h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              View Profile
            </Link>
          </div>
        </div>
      </div>

      {/* User Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {errors.general && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors.general}
            </p>
          </div>
        )}

        <FormCard
          title="Basic Information"
          description="Essential user account details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="name"
              label="Full Name"
              placeholder="Enter full name"
              required
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              error={errors.name}
            />

            <Input
              name="email"
              label="Email Address"
              type="email"
              placeholder="Enter email address"
              required
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              error={errors.email}
            />

            <Select
              name="role"
              label="Role"
              value={formData.role}
              onChange={(e) => handleInputChange("role", e.target.value as UserFormData["role"])}
              options={roleOptions}
              required
              error={errors.role}
            />

            <Select
              name="gender"
              label="Gender"
              value={formData.gender || ""}
              onChange={(e) => handleInputChange("gender", e.target.value || undefined)}
              options={genderOptions}
              error={errors.gender}
            />

            <div className="sm:col-span-2">
              <Select
                name="storeId"
                label="Store"
                value={formData.storeId.toString()}
                onChange={(e) => handleInputChange("storeId", parseInt(e.target.value))}
                options={storeOptions}
                required
                error={errors.storeId}
              />
            </div>
          </FormGrid>
        </FormCard>

        <FormCard
          title="Contact Information"
          description="Phone number and address details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="phone"
              label="Phone Number"
              type="tel"
              placeholder="Enter phone number"
              value={formData.phone || ""}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              error={errors.phone}
            />

            <Input
              name="homeTown"
              label="Home Town"
              placeholder="Enter home town"
              value={formData.homeTown || ""}
              onChange={(e) => handleInputChange("homeTown", e.target.value)}
              error={errors.homeTown}
            />
          </FormGrid>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Address
            </label>
            <textarea
              name="address"
              placeholder="Enter full address"
              rows={3}
              value={formData.address || ""}
              onChange={(e) => handleInputChange("address", e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            {errors.address && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.address}</p>
            )}
          </div>
        </FormCard>

        <FormCard
          title="Staff Information"
          description="Staff ID and employment details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="staffId"
              label="Staff ID"
              placeholder="Enter staff ID (optional)"
              value={formData.staffId || ""}
              onChange={(e) => handleInputChange("staffId", e.target.value)}
              error={errors.staffId}
            />

            <div className="flex items-center">
              <input
                type="checkbox"
                id="active"
                name="active"
                checked={formData.active}
                onChange={(e) => handleInputChange("active", e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="active" className="ml-2 block text-sm text-gray-900 dark:text-white">
                Active Employee
              </label>
            </div>
          </FormGrid>
        </FormCard>

        <FormCard
          title="Emergency Contact"
          description="Emergency contact person details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="contactPerson"
              label="Contact Person Name"
              placeholder="Enter emergency contact name"
              value={formData.contactPerson || ""}
              onChange={(e) => handleInputChange("contactPerson", e.target.value)}
              error={errors.contactPerson}
            />

            <Input
              name="contactPersonPhone"
              label="Contact Person Phone"
              type="tel"
              placeholder="Enter emergency contact phone"
              value={formData.contactPersonPhone || ""}
              onChange={(e) => handleInputChange("contactPersonPhone", e.target.value)}
              error={errors.contactPersonPhone}
            />
          </FormGrid>
        </FormCard>

        <FormCard
          title="Additional Information"
          description="Remarks and comments"
        >
          <FormGrid cols={1} gap="md">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Remarks
              </label>
              <textarea
                name="remarks"
                placeholder="Enter any remarks about the employee"
                rows={3}
                value={formData.remarks || ""}
                onChange={(e) => handleInputChange("remarks", e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              {errors.remarks && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.remarks}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Comments
              </label>
              <textarea
                name="comment"
                placeholder="Enter any additional comments"
                rows={3}
                value={formData.comment || ""}
                onChange={(e) => handleInputChange("comment", e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              {errors.comment && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.comment}</p>
              )}
            </div>
          </FormGrid>
        </FormCard>



        <FormActions align="between">
          <DeleteButton type="button" onClick={handleDelete}>
            Delete User
          </DeleteButton>
          <div className="flex space-x-3">
            <Link href="/users">
              <CancelButton>Cancel</CancelButton>
            </Link>
            <SubmitButton loading={isSubmitting} disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </SubmitButton>
          </div>
        </FormActions>
      </form>
    </div>
  );
}
