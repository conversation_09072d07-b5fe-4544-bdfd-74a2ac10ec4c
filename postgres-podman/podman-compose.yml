version: "3"

services:
  postgres:
    image: docker.io/library/postgres:15-alpine
    container_name: postgres_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: shopper_db
      POSTGRES_USER: shopper_user
      POSTGRES_PASSWORD: shopper_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - postgres_network
    environment:
      TZ: "Africa/Accra"

volumes:
  postgres_data:
    driver: local

networks:
  postgres_network:
    driver: bridge
