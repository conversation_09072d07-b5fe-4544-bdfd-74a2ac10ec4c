import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Creating test users...");

  // Get the first store (assuming it exists)
  const store = await prisma.store.findFirst();
  
  if (!store) {
    console.log("❌ No store found. Please create a store first.");
    return;
  }

  console.log("✅ Using existing store:", store.name);

  // Create or get existing admin user
  let adminUser = await prisma.user.findUnique({
    where: { email: "<EMAIL>" }
  });

  if (!adminUser) {
    const adminPassword = await bcrypt.hash("admin123", 10);
    adminUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        password: adminPassword,
        name: "Admin User",
        role: "ADMIN",
        phone: "+233-20-222-2222",
        address: "Admin HQ",
        storeId: store.id,
      },
    });
    console.log("✅ Admin user created");
  } else {
    console.log("✅ Admin user already exists");
  }

  // Create or get existing cashier user
  let cashierUser = await prisma.user.findUnique({
    where: { email: "<EMAIL>" }
  });

  if (!cashierUser) {
    const cashierPassword = await bcrypt.hash("cashier123", 10);
    cashierUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        password: cashierPassword,
        name: "Cashier User",
        role: "CASHIER",
        phone: "+233-20-333-3333",
        address: "Front Desk",
        storeId: store.id,
      },
    });
    console.log("✅ Cashier user created");
  } else {
    console.log("✅ Cashier user already exists");
  }

  console.log("🎉 Test users setup complete!");
  console.log("📧 Admin login: <EMAIL> / admin123");
  console.log("📧 Cashier login: <EMAIL> / cashier123");
}

main()
  .catch((e) => {
    console.error("❌ Error creating test users", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
