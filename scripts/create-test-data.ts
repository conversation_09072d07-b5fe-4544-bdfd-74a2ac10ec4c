// scripts/create-test-data.ts
import { faker } from "@faker-js/faker";
import { PrismaClient, ProductUnit } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

// ─────────────────────────────────────────────────────────
// CONFIGURE HOW MUCH DATA YOU WANT
// ─────────────────────────────────────────────────────────
const STORE_COUNT = 3;
const CATEGORIES_PER_STORE = 4;
const PRODUCTS_PER_CAT = 25;
const CASHIERS_PER_STORE = 4;
const CUSTOMERS_TOTAL = 150;
const SALES_TOTAL = 500;

async function main() {
  console.log("🌱 Seeding…\n");

  /* ░░░  STORES  ░░░ */
  const stores = await Promise.all(
    Array.from({ length: STORE_COUNT }).map(() =>
      prisma.store.create({
        data: {
          name: faker.company.name(),
          address: faker.location.streetAddress(),
          location: faker.location.city(),
          phoneNumber: faker.phone.number(),
          email: faker.internet.email(),
          ceo: faker.person.fullName(),
          ceoContact: faker.phone.number(),
          ceoEmail: faker.internet.email(),
        },
      }),
    ),
  );
  console.log(`✅  ${stores.length} stores created`);

  /* ░░░  ADMIN USERS  (one global super‑admin) ░░░ */
  const superPwd = await bcrypt.hash("super123", 10);
  await prisma.user.create({
    data: {
      email: "<EMAIL>",
      password: superPwd,
      name: "Super Admin",
      role: "ADMIN",
      storeId: stores[0].id, // arbitrary home store
    },
  });
  console.log("✅  Super admin created");

  /* ░░░  STAFF PER STORE  ░░░ */
  for (const store of stores) {
    const adminPwd = await bcrypt.hash("admin123", 10);
    await prisma.user.create({
      data: {
        email: `admin@${store.name.replace(/\s+/g, "").toLowerCase()}.com`,
        password: adminPwd,
        name: `Admin ${store.name}`,
        role: "ADMIN",
        storeId: store.id,
      },
    });

    const cashierPwd = await bcrypt.hash("cashier123", 10);

    await Promise.all(
      Array.from({ length: CASHIERS_PER_STORE }).map((_, i) =>
        prisma.user.create({
          data: {
            email: `cashier${i + 1}@${store.id}.com`,
            password: cashierPwd,
            name: `Cashier ${i + 1} (${store.name})`,
            role: "CASHIER",
            storeId: store.id,
          },
        }),
      ),
    );
  }
  console.log("✅  Staff created");

  /* ░░░  CATEGORIES (Global)  ░░░ */
  const categories = await Promise.all(
    Array.from({ length: CATEGORIES_PER_STORE }).map((_, i) =>
      prisma.category.create({
        data: {
          name: faker.commerce.department() + ` ${i}`, // ensure uniqueness
          description: faker.commerce.productDescription(),
        },
      }),
    ),
  );
  console.log(`✅  ${categories.length} categories created`);

  /* ░░░  PRODUCTS  ░░░ */
  const allProducts: { id: number; price: number; storeId: number }[] = [];

  for (const store of stores) {
    for (const category of categories) {
      // products in this category
      for (let p = 0; p < PRODUCTS_PER_CAT; p++) {
        const price = parseFloat(
          faker.commerce.price({ min: 0.5, max: 150, dec: 2 }),
        );
        const prod = await prisma.product.create({
          data: {
            name: faker.commerce.productName(),
            slug:
              faker.helpers.slugify(faker.commerce.productName()) +
              `-${category.id}-${store.id}`,
            description: faker.commerce.productDescription(),
            barcode: faker.string.numeric(13),
            sku: faker.string.alphanumeric(8).toUpperCase(),
            price,
            cost: parseFloat((price * 0.6).toFixed(2)),
            stock: faker.number.int({ min: 20, max: 500 }),
            minStock: faker.number.int({ min: 5, max: 30 }),
            unit: faker.helpers.arrayElement([
              ProductUnit.PCS,
              ProductUnit.BOX,
              ProductUnit.PACK,
              ProductUnit.LITER,
              ProductUnit.KG,
            ]),
            categoryId: category.id,
            storeId: store.id,
          },
        });
        allProducts.push({ id: prod.id, price, storeId: store.id });
      }
    }
  }
  console.log(`✅  ${allProducts.length} products created`);

  /* ░░░  CUSTOMERS  ░░░ */
  const customers = await Promise.all(
    Array.from({ length: CUSTOMERS_TOTAL }).map(() =>
      prisma.customer.create({
        data: {
          name: faker.person.fullName(),
          phone: faker.phone.number(),
          email: faker.internet.email(),
          address: faker.location.streetAddress(),
        },
      }),
    ),
  );

  /* ░░░  SALES  (random line items) ░░░ */
  const cashiers = await prisma.user.findMany({ where: { role: "CASHIER" } });
  for (let i = 0; i < SALES_TOTAL; i++) {
    // pick random store & cashier in that store
    const store = faker.helpers.arrayElement(stores);
    const cashier = faker.helpers.arrayElement(
      cashiers.filter((c) => c.storeId === store.id),
    );
    const customer = faker.helpers.arrayElement(customers);
    // pick 1‑5 random products from same store
    const items = faker.helpers
      .arrayElements(
        allProducts.filter((p) => p.storeId === store.id),
        { min: 1, max: 5 },
      )
      .map((p) => {
        const quantity = faker.number.int({ min: 1, max: 10 });
        return {
          productId: p.id,
          quantity,
          price: p.price,
          total: parseFloat((p.price * quantity).toFixed(2)),
        };
      });

    const total = items.reduce((s, it) => s + it.total, 0);
    const tax = parseFloat((total * 0.075).toFixed(2));

    await prisma.sale.create({
      data: {
        total: parseFloat(total.toFixed(2)),
        tax,
        discount: 0.0,
        paymentType: faker.helpers.arrayElement(["CASH", "CARD", "MOBILE"]),
        status: "COMPLETED",
        storeId: store.id,
        userId: cashier.id,
        customerId: customer.id,
        saleItems: { create: items },
      },
    });
  }
  console.log(`✅  ${SALES_TOTAL} random sales created`);

  console.log("\n🎉  Bulk seeding complete!");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
