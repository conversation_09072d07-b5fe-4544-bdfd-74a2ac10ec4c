import { useEffect, useState } from "react";

interface Store {
  id: number;
  name: string;
  address: string;
  location: string;
  phoneNumber: string;
  email: string;
  logoUrl?: string | null;
}

export function useStores() {
  const [stores, setStores] = useState<Store[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStores = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/stores");

      if (!response.ok) {
        throw new Error("Failed to fetch stores");
      }

      const data = await response.json();
      setStores(data.stores || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch stores");
      console.error("Error fetching stores:", err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStores();
  }, []);

  return {
    stores,
    isLoading,
    error,
    refetch: fetchStores,
  };
}

export function useSelectedStore() {
  // For backward compatibility, we'll keep this hook but recommend using useStoreContext from AuthContext
  const [selectedStoreId, setSelectedStoreId] = useState<number | null>(null);
  const { stores, isLoading } = useStores();

  // Auto-select first store if none selected and stores are available
  useEffect(() => {
    if (!selectedStoreId && stores.length > 0 && !isLoading) {
      setSelectedStoreId(stores[0].id);
    }
  }, [stores, isLoading, selectedStoreId]);

  const selectedStore =
    stores.find((store) => store.id === selectedStoreId) || null;

  return {
    selectedStoreId,
    selectedStore,
    setSelectedStoreId,
    stores,
    isLoading,
  };
}
