{
  "folders": [
    {
      "path": "/drive/projects/shopper",
    },
  ],
  "settings": {
    "files.associations": {
      "*.css": "tailwindcss",
      "*.module.css": "tailwindcss",
      "*.module.scss": "scss",
      "*.module.sass": "sass",
      "*.module.less": "less",
      "*.module.styl": "stylus",
    },

    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
      "source.organizeImports": "explicit",
    },
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "tailwindCSS.classAttributes": ["class", "className", "theme"],
    "tailwindCSS.experimental.classRegex": [
      ["twMerge\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
      [
        "createTheme(?:<\\w+>)?\\s*\\(([^)]*)\\)",
        "{?\\s?[\\w].*:\\s*?[\"'`]([^\"'`]*).*?,?\\s?}?",
      ],
    ],
    "typescript.tsdk": "node_modules/typescript/lib",
    // Controls the font family.
    "editor.fontFamily": "MesloLGS NF, Fira Code Nerd, Consolas", //"",
    "editor.fontWeight": "450",
    // Controls the font size.
    "editor.fontSize": 12.5,
    "files.autoSave": "afterDelay",
    "editor.fontVariations": false,

    "terminal.integrated.fontFamily": "MesloLGS NF",
    // "security.workspace.trust.untrustedFiles": "open",
    "workbench.iconTheme": "catppuccin-latte",

    "[html]": {
      "editor.defaultFormatter": "vscode.html-language-features", // "mohd-akram.vscode-html-format"
    },
    "[htm]": {
      "editor.defaultFormatter": "vscode.html-language-features", // "mohd-akram.vscode-html-format"
    },
    "[typescript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[typescriptreact]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[javascript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[javascriptreact]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[json]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[jsonc]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[css]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[scss]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[markdown]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
    },

    "window.zoomLevel": 2,
    "explorer.confirmDragAndDrop": false,
    "html.format.extraLiners": null,
    "html.format.unformatted": "wbr",
    "html.format.wrapAttributesIndentSize": 4,
    "git.openRepositoryInParentFolders": "never",

    // Next.js specific settings
    "typescript.preferences.importModuleSpecifier": "non-relative",
    "typescript.preferences.quoteStyle": "single",
    "javascript.preferences.importModuleSpecifier": "non-relative",
    "javascript.preferences.quoteStyle": "single",
    "typescript.updateImportsOnFileMove.enabled": "always",
    "javascript.updateImportsOnFileMove.enabled": "always",
    "search.exclude": {
      "**/node_modules": true,
      "**/bower_components": true,
      "**/tmp": true,
      "**/dist": true,
      "**/.next": true,
      "**/out": true,
    },
    "files.exclude": {
      "**/.git": true,
      "**/.svn": true,
      "**/.hg": true,
      "**/CVS": true,
      "**/.DS_Store": true,
      "**/Thumbs.db": true,
      "**/.next": true,
      "**/out": true,
    },
  },
  "extensions": {
    "recommendations": [
      "bradlc.vscode-tailwindcss",
      "dbaeumer.vscode-eslint",
      "esbenp.prettier-vscode",
      "yoavbls.pretty-ts-errors",
      "ms-vscode.vscode-typescript-next",
      "formulahendry.auto-rename-tag",
      "naumovs.color-highlight",
      "christian-kohler.path-intellisense",
      "mikestead.dotenv",
      "dsznajder.es7-react-js-snippets",
      "wix.vscode-import-cost",
      "catppuccin.catppuccin-vsc-pack",
    ],
  },
}
