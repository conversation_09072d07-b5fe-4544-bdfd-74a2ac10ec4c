import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Seeding database...");

  // Create or get existing store
  let store = await prisma.store.findFirst({
    where: { name: "Downtown Supermart" },
  });

  if (!store) {
    store = await prisma.store.create({
      data: {
        name: "Downtown Supermart",
        address: "123 Main Street",
        location: "Central Tamale",
        phoneNumber: "+233-24-000-0000",
        email: "<EMAIL>",
        website: "https://supermart.com",
        ceo: "<PERSON><PERSON><PERSON>",
        ceoContact: "+233-24-111-1111",
        ceoEmail: "<EMAIL>",
      },
    });
  }

  console.log("✅ Store created");

  // Create categories
  const categories = await Promise.all([
    prisma.category.create({
      data: { name: "Beverages", description: "Drinks and beverages" },
    }),
    prisma.category.create({
      data: { name: "Snacks", description: "Snacks and confectionery" },
    }),
    prisma.category.create({
      data: { name: "Dairy", description: "Dairy products" },
    }),
    prisma.category.create({
      data: {
        name: "Household",
        description: "Household items and cleaning supplies",
      },
    }),
  ]);

  console.log("✅ Categories created");

  // Create or get existing users
  let adminUser = await prisma.user.findUnique({
    where: { email: "<EMAIL>" },
  });

  if (!adminUser) {
    const adminPassword = await bcrypt.hash("admin123", 10);
    adminUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        password: adminPassword,
        name: "Admin User",
        role: "ADMIN",
        phone: "+233-20-222-2222",
        address: "Admin HQ",
        storeId: store.id,
      },
    });
  }

  let cashierUser = await prisma.user.findUnique({
    where: { email: "<EMAIL>" },
  });

  if (!cashierUser) {
    const cashierPassword = await bcrypt.hash("cashier123", 10);
    cashierUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        password: cashierPassword,
        name: "Cashier User",
        role: "CASHIER",
        phone: "+233-20-333-3333",
        address: "Front Desk",
        storeId: store.id,
      },
    });
  }

  console.log("✅ Users created");

  // Create products
  const products = await Promise.all([
    prisma.product.create({
      data: {
        name: "Coca Cola 500ml",
        slug: "coca-cola-500ml",
        description: "Refreshing cola drink",
        barcode: "1234567890123",
        sku: "COKE-500",
        price: 2.5,
        cost: 1.5,
        stock: 100,
        minStock: 20,
        unit: "PCS",
        categoryId: categories[0].id,
        storeId: store.id,
      },
    }),
    prisma.product.create({
      data: {
        name: "Potato Chips",
        slug: "potato-chips",
        description: "Crispy potato chips",
        barcode: "1234567890125",
        sku: "CHIPS-REG",
        price: 3.0,
        cost: 1.8,
        stock: 75,
        minStock: 15,
        unit: "PCS",
        categoryId: categories[1].id,
        storeId: store.id,
      },
    }),
    prisma.product.create({
      data: {
        name: "Milk 1L",
        slug: "milk-1l",
        description: "Fresh whole milk",
        barcode: "1234567890127",
        sku: "MILK-1L",
        price: 3.5,
        cost: 2.5,
        stock: 50,
        minStock: 10,
        unit: "LITER",
        categoryId: categories[2].id,
        storeId: store.id,
      },
    }),
  ]);

  console.log("✅ Products created");

  // Create a sample sale
  const sale = await prisma.sale.create({
    data: {
      total: 9.0,
      tax: 0.5,
      paymentType: "CASH",
      status: "COMPLETED",
      userId: cashierUser.id,
      storeId: store.id,
      saleItems: {
        create: [
          {
            quantity: 2,
            price: 2.5,
            total: 5.0,
            productId: products[0].id, // Coca Cola
          },
          {
            quantity: 1,
            price: 3.0,
            total: 3.0,
            productId: products[1].id, // Chips
          },
        ],
      },
    },
  });

  console.log("✅ Sample sale created");

  // Optional: Add stock adjustment
  await prisma.stockAdjustment.create({
    data: {
      type: "REMOVE",
      quantity: 2,
      reason: "Sold via POS",
      userId: cashierUser.id,
      productId: products[0].id,
    },
  });

  // Optional: Add a todo
  await prisma.todo.create({
    data: {
      toDo: "Reorder Coca Cola",
      isPersonal: true,
      userId: adminUser.id,
    },
  });

  console.log("🎉 Seeding complete!");
}

main()
  .catch((e) => {
    console.error("❌ Seeding error", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
