-- Adjust table names if you changed @@map names
-- 1. FUNCTION
CREATE OR REPLACE FUNCTION update_product_stock()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE products SET stock = stock - NEW.quantity
    WHERE id = NEW.product_id;
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    -- if quantity OR product changed, compute delta
    IF NEW.product_id = OLD.product_id THEN
      UPDATE products
      SET stock = stock + OLD.quantity - NEW.quantity
      WHERE id = NEW.product_id;
    ELSE
      -- revert old product, subtract from new product
      UPDATE products SET stock = stock + OLD.quantity WHERE id = OLD.product_id;
      UPDATE products SET stock = stock - NEW.quantity WHERE id = NEW.product_id;
    END IF;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE products SET stock = stock + OLD.quantity
    WHERE id = OLD.product_id;
    RETURN OLD;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 2. TRIGGER
DROP TRIGGER IF EXISTS trg_update_stock ON sale_items;
CREATE TRIGGER trg_update_stock
AFTER INSERT OR UPDATE OR DELETE ON sale_items
FOR EACH ROW EXECUTE FUNCTION update_product_stock();
