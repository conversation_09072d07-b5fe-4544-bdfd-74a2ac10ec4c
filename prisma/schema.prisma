generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ===== ENUMS =====
enum Role {
  ADMIN
  MANAGER
  CASHIER
  STAFF
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum PaymentType {
  CASH
  CARD
  MOBILE
  TRANSFER
}

enum SaleStatus {
  PENDING
  COMPLETED
  CANCELLED
  REFUNDED
}

enum AdjustmentType {
  ADD
  REMOVE
  SET
}

enum ProductUnit {
  PCS
  LITER
  KG
  BAG
  BOX
  TIN
  PACK
}

// ===== MODELS =====

// Store Information
model Store {
  id              Int       @id @default(autoincrement())
  name            String    @unique
  address         String
  location        String
  phoneNumber     String
  email           String
  website         String?
  logoUrl         String?
  ceo             String
  ceoContact      String
  ceoEmail        String
  facebook        String?
  whatsapp        String?
  telegram        String?
  visionStatement String?
  missionStatement String?
  mainGoal        String?
  mainObjective   String?

  // Relations
  users      User[]
  products   Product[]
  sales      Sale[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("stores")
}

// Users (Staffs)
model User {
  id          Int       @id @default(autoincrement())
  email       String    @unique
  password    String
  name        String
  role        Role      @default(CASHIER)
  gender      Gender?
  phone       String?
  address     String?
  homeTown    String?
  staffId     String?   @unique
  contactPerson       String?
  contactPersonPhone  String?
  active      Boolean   @default(true)
  remarks     String?
  comment     String?

  storeId     Int
  store       Store     @relation(fields: [storeId], references: [id])

  // Relations
  sales             Sale[]
  stockAdjustments  StockAdjustment[]
  todos             Todo[]

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("users")
}

// Customers (optional)
model Customer {
  id          Int      @id @default(autoincrement())
  name        String
  email       String?
  phone       String?
  address     String?

  sales       Sale[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("customers")
}

// Categories
model Category {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  products    Product[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("categories")
}

// Products
model Product {
  id          Int      @id @default(autoincrement())
  name        String
  slug        String   @unique
  description String?
  barcode     String?  @unique
  sku         String?  @unique
  unit        ProductUnit @default(PCS)
  price       Float
  cost        Float?
  stock       Int      @default(0)
  minStock    Int      @default(0)
  isActive    Boolean  @default(true)

  categoryId  Int
  storeId     Int

  category    Category @relation(fields: [categoryId], references: [id])
  store       Store    @relation(fields: [storeId], references: [id])

  saleItems   SaleItem[]
  stockAdjustments StockAdjustment[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("products")
}

// Sales
model Sale {
  id          Int        @id @default(autoincrement())
  total       Float
  tax         Float?
  discount    Float?
  paymentType PaymentType @default(CASH)
  status      SaleStatus  @default(COMPLETED)
  refNumber   String      @default(uuid()) // Can use UUID or custom generator

  userId      Int
  storeId     Int
  customerId  Int?

  user        User       @relation(fields: [userId], references: [id])
  store       Store      @relation(fields: [storeId], references: [id])
  customer    Customer?  @relation(fields: [customerId], references: [id])

  saleItems   SaleItem[]

  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  @@map("sales")
}

// Sale Items
model SaleItem {
  id        Int     @id @default(autoincrement())
  quantity  Int
  price     Float
  total     Float
  saleId    Int
  productId Int

  sale      Sale    @relation(fields: [saleId], references: [id])
  product   Product @relation(fields: [productId], references: [id])

  @@map("sale_items")
}

// Stock Adjustment
model StockAdjustment {
  id        Int            @id @default(autoincrement())
  quantity  Int
  type      AdjustmentType
  reason    String?
  productId Int
  userId    Int

  product   Product @relation(fields: [productId], references: [id])
  user      User    @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())

  @@map("stock_adjustments")
}

// Todo (Personal tasks/reminders)
model Todo {
  id        Int     @id @default(autoincrement())
  toDo      String
  isPersonal Boolean @default(false)
  isDone     Boolean @default(false)
  userId     Int

  user      User    @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())

  @@map("todos")
}
