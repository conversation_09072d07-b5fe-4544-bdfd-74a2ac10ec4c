import { PrismaClient } from "@prisma/client";

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: process.env.NODE_ENV === "development" ? ["query"] : [],
  });

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Database utility functions
export const db = {
  // Product operations
  products: {
    getAll: () =>
      prisma.product.findMany({
        include: { category: true },
        orderBy: { name: "asc" },
      }),

    getById: (id: string) =>
      prisma.product.findUnique({
        where: { id },
        include: { category: true },
      }),

    getByBarcode: (barcode: string) =>
      prisma.product.findUnique({
        where: { barcode },
        include: { category: true },
      }),

    getLowStock: async () => {
      const products = await prisma.product.findMany({
        include: { category: true },
      });
      return products.filter((product) => product.stock <= product.minStock);
    },

    updateStock: (id: string, quantity: number) =>
      prisma.product.update({
        where: { id },
        data: { stock: quantity },
      }),
  },

  // Category operations
  categories: {
    getAll: () =>
      prisma.category.findMany({
        include: { _count: { select: { products: true } } },
        orderBy: { name: "asc" },
      }),

    getById: (id: string) =>
      prisma.category.findUnique({
        where: { id },
        include: { products: true },
      }),
  },

  // Sales operations
  sales: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    create: (data: any) =>
      prisma.sale.create({
        data,
        include: {
          saleItems: {
            include: { product: true },
          },
          user: true,
        },
      }),

    getAll: (limit = 50) =>
      prisma.sale.findMany({
        take: limit,
        include: {
          saleItems: {
            include: { product: true },
          },
          user: true,
        },
        orderBy: { createdAt: "desc" },
      }),

    getById: (id: string) =>
      prisma.sale.findUnique({
        where: { id },
        include: {
          saleItems: {
            include: { product: true },
          },
          user: true,
        },
      }),

    getTodaysSales: () => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      return prisma.sale.findMany({
        where: {
          createdAt: {
            gte: today,
            lt: tomorrow,
          },
        },
        include: {
          saleItems: {
            include: { product: true },
          },
        },
      });
    },
  },

  // Store operations
  stores: {
    getAll: () =>
      prisma.store.findMany({
        orderBy: { name: "asc" },
      }),

    getById: (id: number) =>
      prisma.store.findUnique({
        where: { id },
      }),
  },

  // User operations
  users: {
    getAll: () =>
      prisma.user.findMany({
        orderBy: { name: "asc" },
      }),

    getById: (id: number) =>
      prisma.user.findUnique({
        where: { id },
        include: {
          store: true,
        },
      }),

    getByEmail: (email: string) =>
      prisma.user.findUnique({
        where: { email },
        include: {
          store: true,
        },
      }),
  },
};
