"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";

// ============================================================================
// TYPES
// ============================================================================

export interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface PaginationActions {
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  goToFirstPage: () => void;
  goToLastPage: () => void;
}

export interface UsePaginationOptions {
  initialPage?: number;
  initialPageSize?: number;
  totalItems: number;
  useUrlParams?: boolean;
  pageParam?: string;
  pageSizeParam?: string;
}

export interface UsePaginationReturn
  extends PaginationState,
    PaginationActions {
  canGoNext: boolean;
  canGoPrevious: boolean;
  startItem: number;
  endItem: number;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Calculate pagination metadata
 */
export function calculatePagination(
  currentPage: number,
  pageSize: number,
  totalItems: number,
): PaginationState {
  const totalPages = Math.ceil(totalItems / pageSize);

  return {
    currentPage: Math.max(1, Math.min(currentPage, totalPages)),
    pageSize,
    totalItems,
    totalPages,
  };
}

/**
 * Get pagination slice for array data
 */
export function getPaginationSlice<T>(
  data: T[],
  currentPage: number,
  pageSize: number,
): T[] {
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return data.slice(startIndex, endIndex);
}

/**
 * Build URL query parameters for pagination
 */
export function buildPaginationQuery(
  currentPage: number,
  pageSize: number,
  pageParam: string = "page",
  pageSizeParam: string = "pageSize",
): Record<string, string> {
  return {
    [pageParam]: currentPage.toString(),
    [pageSizeParam]: pageSize.toString(),
  };
}

// ============================================================================
// HOOKS
// ============================================================================

/**
 * Hook for managing pagination state with optional URL synchronization
 */
export function usePagination({
  initialPage = 1,
  initialPageSize = 10,
  totalItems,
  useUrlParams = false,
  pageParam = "page",
  pageSizeParam = "pageSize",
}: UsePaginationOptions): UsePaginationReturn {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize state from URL params or defaults
  const getInitialPage = useCallback(() => {
    if (useUrlParams && searchParams) {
      const urlPage = searchParams.get(pageParam);
      return urlPage ? parseInt(urlPage, 10) : initialPage;
    }
    return initialPage;
  }, [useUrlParams, searchParams, pageParam, initialPage]);

  const getInitialPageSize = useCallback(() => {
    if (useUrlParams && searchParams) {
      const urlPageSize = searchParams.get(pageSizeParam);
      return urlPageSize ? parseInt(urlPageSize, 10) : initialPageSize;
    }
    return initialPageSize;
  }, [useUrlParams, searchParams, pageSizeParam, initialPageSize]);

  const [currentPage, setCurrentPage] = useState(getInitialPage);
  const [pageSize, setPageSizeState] = useState(getInitialPageSize);

  // Calculate derived state
  const pagination = calculatePagination(currentPage, pageSize, totalItems);
  const startItem = totalItems > 0 ? (currentPage - 1) * pageSize + 1 : 0;
  const endItem = Math.min(currentPage * pageSize, totalItems);
  const canGoNext = currentPage < pagination.totalPages;
  const canGoPrevious = currentPage > 1;

  // Update URL when pagination changes
  const updateUrl = useCallback(
    (page: number, size: number) => {
      if (!useUrlParams || !router) return;

      const params = new URLSearchParams(searchParams?.toString());
      params.set(pageParam, page.toString());
      params.set(pageSizeParam, size.toString());

      router.push(`?${params.toString()}`, { scroll: false });
    },
    [useUrlParams, router, searchParams, pageParam, pageSizeParam],
  );

  // Sync with URL params when they change
  useEffect(() => {
    if (useUrlParams) {
      const urlPage = getInitialPage();
      const urlPageSize = getInitialPageSize();

      if (urlPage !== currentPage) {
        setCurrentPage(urlPage);
      }
      if (urlPageSize !== pageSize) {
        setPageSizeState(urlPageSize);
      }
    }
  }, [useUrlParams, getInitialPage, getInitialPageSize, currentPage, pageSize]);

  // Actions
  const setPage = useCallback(
    (page: number) => {
      const validPage = Math.max(1, Math.min(page, pagination.totalPages));
      setCurrentPage(validPage);
      updateUrl(validPage, pageSize);
    },
    [pagination.totalPages, pageSize, updateUrl],
  );

  const setPageSize = useCallback(
    (size: number) => {
      const newPageSize = Math.max(1, size);
      // Recalculate current page to maintain position
      const currentItem = (currentPage - 1) * pageSize + 1;
      const newPage = Math.max(1, Math.ceil(currentItem / newPageSize));

      setPageSizeState(newPageSize);
      setCurrentPage(newPage);
      updateUrl(newPage, newPageSize);
    },
    [currentPage, pageSize, updateUrl],
  );

  const nextPage = useCallback(() => {
    if (canGoNext) {
      setPage(currentPage + 1);
    }
  }, [canGoNext, currentPage, setPage]);

  const previousPage = useCallback(() => {
    if (canGoPrevious) {
      setPage(currentPage - 1);
    }
  }, [canGoPrevious, currentPage, setPage]);

  const goToFirstPage = useCallback(() => {
    setPage(1);
  }, [setPage]);

  const goToLastPage = useCallback(() => {
    setPage(pagination.totalPages);
  }, [setPage, pagination.totalPages]);

  return {
    // State
    currentPage: pagination.currentPage,
    pageSize: pagination.pageSize,
    totalItems: pagination.totalItems,
    totalPages: pagination.totalPages,
    canGoNext,
    canGoPrevious,
    startItem,
    endItem,

    // Actions
    setPage,
    setPageSize,
    nextPage,
    previousPage,
    goToFirstPage,
    goToLastPage,
  };
}

/**
 * Hook for client-side pagination of array data
 */
export function useClientPagination<T>(
  data: T[],
  initialPageSize: number = 10,
  useUrlParams: boolean = false,
) {
  const pagination = usePagination({
    totalItems: data.length,
    initialPageSize,
    useUrlParams,
  });

  const paginatedData = getPaginationSlice(
    data,
    pagination.currentPage,
    pagination.pageSize,
  );

  return {
    ...pagination,
    data: paginatedData,
  };
}

/**
 * Hook for server-side pagination
 */
export function useServerPagination({
  totalItems,
  initialPage = 1,
  initialPageSize = 10,
  useUrlParams = true,
}: Omit<UsePaginationOptions, "totalItems"> & { totalItems: number }) {
  const pagination = usePagination({
    totalItems,
    initialPage,
    initialPageSize,
    useUrlParams,
  });

  // Calculate offset for server queries
  const offset = (pagination.currentPage - 1) * pagination.pageSize;
  const limit = pagination.pageSize;

  return {
    ...pagination,
    offset,
    limit,
    // Helper for building API query parameters
    getQueryParams: () => ({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      offset,
    }),
  };
}

/**
 * Hook for managing pagination with search functionality
 */
export function usePaginationWithSearch<T>(
  data: T[],
  searchFields: (keyof T)[],
  initialPageSize: number = 10,
) {
  const [searchQuery, setSearchQuery] = useState("");

  // Filter data based on search query
  const filteredData = useMemo(() => {
    if (!searchQuery.trim()) return data;

    return data.filter((item) =>
      searchFields.some((field) => {
        const value = item[field];
        if (typeof value === "string") {
          return value.toLowerCase().includes(searchQuery.toLowerCase());
        }
        if (typeof value === "number") {
          return value.toString().includes(searchQuery);
        }
        return false;
      }),
    );
  }, [data, searchQuery, searchFields]);

  const pagination = useClientPagination(filteredData, initialPageSize);

  return {
    ...pagination,
    searchQuery,
    setSearchQuery,
    totalUnfilteredItems: data.length,
  };
}

/**
 * Hook for managing table sorting with pagination
 */
export function useSortablePagination<T>(
  data: T[],
  initialPageSize: number = 10,
) {
  const [sortConfig, setSortConfig] = useState<{
    key: keyof T | null;
    direction: "asc" | "desc";
  }>({ key: null, direction: "asc" });

  // Sort data based on sort configuration
  const sortedData = useMemo(() => {
    if (!sortConfig.key) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key!];
      const bValue = b[sortConfig.key!];

      if (aValue < bValue) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });
  }, [data, sortConfig]);

  const pagination = useClientPagination(sortedData, initialPageSize);

  const handleSort = (key: keyof T) => {
    setSortConfig((prev) => ({
      key,
      direction: prev.key === key && prev.direction === "asc" ? "desc" : "asc",
    }));
  };

  return {
    ...pagination,
    sortConfig,
    handleSort,
  };
}
